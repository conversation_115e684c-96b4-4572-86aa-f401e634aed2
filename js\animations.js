// ===== ANIMATIONS & INTERACTIONS =====

// Counter animation for statistics
class CounterAnimation {
    constructor() {
        this.counters = document.querySelectorAll('.stat-number[data-count]');
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver(this.handleIntersection.bind(this), {
                threshold: 0.5
            });

            this.counters.forEach(counter => {
                observer.observe(counter);
            });
        }
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                this.animateCounter(entry.target);
                entry.target.classList.add('animated');
            }
        });
    }

    animateCounter(element) {
        const target = parseInt(element.dataset.count);
        const duration = 2000;
        const start = performance.now();
        const startValue = 0;

        const animate = (currentTime) => {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const current = Math.floor(startValue + (target - startValue) * easeOut);
            
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.textContent = target;
            }
        };

        requestAnimationFrame(animate);
    }
}

// Skill bar animation
class SkillBarAnimation {
    constructor() {
        this.skillBars = document.querySelectorAll('.skill-progress[data-width]');
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver(this.handleIntersection.bind(this), {
                threshold: 0.3
            });

            this.skillBars.forEach(bar => {
                observer.observe(bar);
            });
        }
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                this.animateSkillBar(entry.target);
                entry.target.classList.add('animated');
            }
        });
    }

    animateSkillBar(element) {
        const targetWidth = element.dataset.width;
        element.style.width = `${targetWidth}%`;
    }
}

// Typewriter effect
class TypewriterEffect {
    constructor(element, text, speed = 100) {
        this.element = element;
        this.text = text;
        this.speed = speed;
        this.currentIndex = 0;
        this.isDeleting = false;
    }

    start() {
        this.type();
    }

    type() {
        const current = this.text[this.currentIndex];
        const displayText = this.isDeleting 
            ? current.substring(0, current.length - 1)
            : current.substring(0, this.element.textContent.length + 1);

        this.element.textContent = displayText;

        let typeSpeed = this.speed;

        if (this.isDeleting) {
            typeSpeed /= 2;
        }

        if (!this.isDeleting && displayText === current) {
            typeSpeed = 2000; // Pause at end
            this.isDeleting = true;
        } else if (this.isDeleting && displayText === '') {
            this.isDeleting = false;
            this.currentIndex = (this.currentIndex + 1) % this.text.length;
            typeSpeed = 500;
        }

        setTimeout(() => this.type(), typeSpeed);
    }
}

// Parallax scrolling effect
class ParallaxEffect {
    constructor() {
        this.parallaxElements = document.querySelectorAll('.parallax');
        this.init();
    }

    init() {
        if (this.parallaxElements.length > 0) {
            window.addEventListener('scroll', this.handleScroll.bind(this));
        }
    }

    handleScroll() {
        const scrollTop = window.pageYOffset;

        this.parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrollTop * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }
}

// Particle system
class ParticleSystem {
    constructor(container) {
        this.container = container;
        this.particles = [];
        this.particleCount = 50;
        this.init();
    }

    init() {
        this.createParticles();
        this.animate();
    }

    createParticles() {
        for (let i = 0; i < this.particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random properties
            const size = Math.random() * 4 + 2;
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            const speed = Math.random() * 2 + 1;
            const opacity = Math.random() * 0.5 + 0.1;
            
            particle.style.cssText = `
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                opacity: ${opacity};
                animation-duration: ${speed * 10}s;
                animation-delay: ${Math.random() * 20}s;
            `;
            
            this.container.appendChild(particle);
            this.particles.push({
                element: particle,
                x: x,
                y: y,
                speed: speed,
                size: size
            });
        }
    }

    animate() {
        this.particles.forEach(particle => {
            particle.y += particle.speed;
            
            if (particle.y > window.innerHeight) {
                particle.y = -particle.size;
                particle.x = Math.random() * window.innerWidth;
            }
            
            particle.element.style.top = `${particle.y}px`;
            particle.element.style.left = `${particle.x}px`;
        });
        
        requestAnimationFrame(() => this.animate());
    }
}

// Cursor effect
class CursorEffect {
    constructor() {
        this.cursor = null;
        this.follower = null;
        this.init();
    }

    init() {
        // Create cursor elements
        this.cursor = document.createElement('div');
        this.cursor.className = 'custom-cursor';
        
        this.follower = document.createElement('div');
        this.follower.className = 'cursor-follower';
        
        document.body.appendChild(this.cursor);
        document.body.appendChild(this.follower);
        
        this.bindEvents();
    }

    bindEvents() {
        let mouseX = 0, mouseY = 0;
        let followerX = 0, followerY = 0;
        
        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
            
            this.cursor.style.left = `${mouseX}px`;
            this.cursor.style.top = `${mouseY}px`;
        });
        
        // Smooth follower animation
        const animateFollower = () => {
            followerX += (mouseX - followerX) * 0.1;
            followerY += (mouseY - followerY) * 0.1;
            
            this.follower.style.left = `${followerX}px`;
            this.follower.style.top = `${followerY}px`;
            
            requestAnimationFrame(animateFollower);
        };
        
        animateFollower();
        
        // Hover effects
        const hoverElements = document.querySelectorAll('a, button, .hover-effect');
        hoverElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                this.cursor.classList.add('hover');
                this.follower.classList.add('hover');
            });
            
            element.addEventListener('mouseleave', () => {
                this.cursor.classList.remove('hover');
                this.follower.classList.remove('hover');
            });
        });
    }
}

// Morphing shapes
class MorphingShapes {
    constructor() {
        this.shapes = document.querySelectorAll('.morph');
        this.init();
    }

    init() {
        this.shapes.forEach(shape => {
            this.startMorphing(shape);
        });
    }

    startMorphing(shape) {
        const morphs = [
            '60% 40% 30% 70% / 60% 30% 70% 40%',
            '30% 60% 70% 40% / 50% 60% 30% 60%',
            '40% 50% 60% 30% / 70% 40% 50% 60%',
            '50% 30% 40% 60% / 40% 70% 60% 30%'
        ];
        
        let currentMorph = 0;
        
        setInterval(() => {
            currentMorph = (currentMorph + 1) % morphs.length;
            shape.style.borderRadius = morphs[currentMorph];
        }, 3000);
    }
}

// Loading screen animation
class LoadingAnimation {
    constructor() {
        this.loadingScreen = document.getElementById('loadingScreen');
        this.init();
    }

    init() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.hideLoading();
            }, 1000);
        });
    }

    hideLoading() {
        if (this.loadingScreen) {
            this.loadingScreen.classList.add('hidden');
            document.body.classList.remove('loading');
            
            setTimeout(() => {
                this.loadingScreen.remove();
            }, 500);
        }
    }
}

// Text reveal animation
class TextRevealAnimation {
    constructor() {
        this.textElements = document.querySelectorAll('.text-reveal');
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver(this.handleIntersection.bind(this), {
                threshold: 0.1
            });

            this.textElements.forEach(element => {
                this.wrapWords(element);
                observer.observe(element);
            });
        }
    }

    wrapWords(element) {
        const words = element.textContent.split(' ');
        element.innerHTML = '';
        
        words.forEach((word, index) => {
            const span = document.createElement('span');
            span.textContent = word + ' ';
            span.style.display = 'inline-block';
            span.style.transform = 'translateY(100%)';
            span.style.transition = `transform 0.6s ease ${index * 0.1}s`;
            element.appendChild(span);
        });
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const words = entry.target.querySelectorAll('span');
                words.forEach(word => {
                    word.style.transform = 'translateY(0)';
                });
            }
        });
    }
}

// Magnetic effect for buttons
class MagneticEffect {
    constructor() {
        this.magneticElements = document.querySelectorAll('.magnetic');
        this.init();
    }

    init() {
        this.magneticElements.forEach(element => {
            element.addEventListener('mousemove', (e) => {
                this.handleMouseMove(e, element);
            });
            
            element.addEventListener('mouseleave', () => {
                this.resetElement(element);
            });
        });
    }

    handleMouseMove(e, element) {
        const rect = element.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        const strength = 0.3;
        const translateX = x * strength;
        const translateY = y * strength;
        
        element.style.transform = `translate(${translateX}px, ${translateY}px)`;
    }

    resetElement(element) {
        element.style.transform = 'translate(0, 0)';
    }
}

// Smooth scrolling with easing
class SmoothScroll {
    constructor() {
        this.isScrolling = false;
        this.init();
    }

    init() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = anchor.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    this.scrollToElement(targetElement);
                }
            });
        });
    }

    scrollToElement(element) {
        if (this.isScrolling) return;
        
        this.isScrolling = true;
        const targetPosition = element.offsetTop - 80;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        const duration = 1000;
        let startTime = null;
        
        const animation = (currentTime) => {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);
            
            // Easing function (ease-in-out-cubic)
            const easeInOutCubic = progress < 0.5 
                ? 4 * progress * progress * progress 
                : 1 - Math.pow(-2 * progress + 2, 3) / 2;
            
            window.scrollTo(0, startPosition + distance * easeInOutCubic);
            
            if (timeElapsed < duration) {
                requestAnimationFrame(animation);
            } else {
                this.isScrolling = false;
            }
        };
        
        requestAnimationFrame(animation);
    }
}

// Image hover effects
class ImageHoverEffects {
    constructor() {
        this.images = document.querySelectorAll('.hover-image');
        this.init();
    }

    init() {
        this.images.forEach(image => {
            image.addEventListener('mouseenter', () => {
                this.addHoverEffect(image);
            });
            
            image.addEventListener('mouseleave', () => {
                this.removeHoverEffect(image);
            });
        });
    }

    addHoverEffect(image) {
        image.style.transform = 'scale(1.05)';
        image.style.filter = 'brightness(1.1) contrast(1.1)';
    }

    removeHoverEffect(image) {
        image.style.transform = 'scale(1)';
        image.style.filter = 'brightness(1) contrast(1)';
    }
}

// Back to top button functionality
class BackToTopButton {
    constructor() {
        this.button = document.getElementById('backToTop');
        this.init();
    }

    init() {
        if (this.button) {
            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    this.button.classList.add('show');
                } else {
                    this.button.classList.remove('show');
                }
            });

            this.button.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    }
}

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CounterAnimation();
    new SkillBarAnimation();
    new ParallaxEffect();
    new LoadingAnimation();
    new TextRevealAnimation();
    new MagneticEffect();
    new SmoothScroll();
    new ImageHoverEffects();
    new MorphingShapes();
    new BackToTopButton();
    
    // Initialize particle system for hero section
    const heroParticles = document.querySelector('.hero-particles');
    if (heroParticles) {
        new ParticleSystem(heroParticles);
    }
    
    // Initialize cursor effect (optional, for desktop only)
    if (window.innerWidth > 1024) {
        new CursorEffect();
    }
    
    // Initialize typewriter effect for hero title
    const heroTitle = document.querySelector('.title-accent');
    if (heroTitle) {
        const typewriter = new TypewriterEffect(
            heroTitle,
            ['Web Developer & Designer', 'Frontend Specialist', 'UI/UX Enthusiast', 'Problem Solver'],
            150
        );
        
        setTimeout(() => {
            typewriter.start();
        }, 2000);
    }
});

// Scroll-based animations
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.parallax-bg');
    
    parallaxElements.forEach(element => {
        const speed = element.dataset.speed || 0.5;
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
});

// Resize handler for responsive animations
window.addEventListener('resize', window.utils?.debounce ? window.utils.debounce(() => {
    // Recalculate particle positions
    const particles = document.querySelectorAll('.particle');
    particles.forEach(particle => {
        particle.style.left = Math.random() * window.innerWidth + 'px';
    });
}, 250) : () => {});

// Export animation classes
window.animations = {
    CounterAnimation,
    SkillBarAnimation,
    TypewriterEffect,
    ParallaxEffect,
    ParticleSystem,
    CursorEffect,
    MorphingShapes,
    LoadingAnimation,
    TextRevealAnimation,
    MagneticEffect,
    SmoothScroll,
    ImageHoverEffects,
    BackToTopButton
};