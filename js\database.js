// ===== DATABASE MANAGEMENT (IndexedDB) =====

class PortfolioDatabase {
    constructor() {
        this.dbName = 'PortfolioData';
        this.version = 1;
        this.db = null;
        this.initDatabase();
    }

    async initDatabase() {
        try {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => {
                console.error('Database error:', request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('Database initialized successfully');
                this.seedInitialData();
            };

            request.onupgradeneeded = (event) => {
                this.db = event.target.result;
                this.createObjectStores();
            };
        } catch (error) {
            console.error('Failed to initialize database:', error);
        }
    }

    createObjectStores() {
        // Projects store
        if (!this.db.objectStoreNames.contains('projects')) {
            const projectStore = this.db.createObjectStore('projects', { keyPath: 'id', autoIncrement: true });
            projectStore.createIndex('category', 'category', { unique: false });
            projectStore.createIndex('featured', 'featured', { unique: false });
            projectStore.createIndex('date', 'date', { unique: false });
        }

        // Skills store
        if (!this.db.objectStoreNames.contains('skills')) {
            const skillsStore = this.db.createObjectStore('skills', { keyPath: 'id', autoIncrement: true });
            skillsStore.createIndex('category', 'category', { unique: false });
            skillsStore.createIndex('level', 'level', { unique: false });
        }

        // Experience store
        if (!this.db.objectStoreNames.contains('experience')) {
            const experienceStore = this.db.createObjectStore('experience', { keyPath: 'id', autoIncrement: true });
            experienceStore.createIndex('type', 'type', { unique: false });
            experienceStore.createIndex('startDate', 'startDate', { unique: false });
        }

        // Contact messages store
        if (!this.db.objectStoreNames.contains('messages')) {
            const messagesStore = this.db.createObjectStore('messages', { keyPath: 'id', autoIncrement: true });
            messagesStore.createIndex('date', 'date', { unique: false });
            messagesStore.createIndex('read', 'read', { unique: false });
        }

        // Analytics store
        if (!this.db.objectStoreNames.contains('analytics')) {
            const analyticsStore = this.db.createObjectStore('analytics', { keyPath: 'id', autoIncrement: true });
            analyticsStore.createIndex('event', 'event', { unique: false });
            analyticsStore.createIndex('date', 'date', { unique: false });
        }

        // Blog posts store
        if (!this.db.objectStoreNames.contains('blogPosts')) {
            const blogStore = this.db.createObjectStore('blogPosts', { keyPath: 'id', autoIncrement: true });
            blogStore.createIndex('published', 'published', { unique: false });
            blogStore.createIndex('date', 'date', { unique: false });
        }

        console.log('Object stores created successfully');
    }

    async seedInitialData() {
        try {
            // Check if data already exists
            const projectCount = await this.getCount('projects');
            if (projectCount > 0) {
                console.log('Data already exists, skipping seed');
                return;
            }

            // Seed projects
            const projects = [
                {
                    title: 'E-Commerce Platform',
                    description: 'A full-stack e-commerce solution built with React and Node.js, featuring payment integration, inventory management, and admin dashboard.',
                    category: 'web',
                    image: 'assets/images/projects/ecommerce.jpg',
                    tags: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Express'],
                    featured: true,
                    date: '2024-01-15',
                    githubUrl: 'https://github.com/mohammed-alashrafi/ecommerce-platform',
                    liveUrl: 'https://ecommerce-demo.malashrafi.dev',
                    technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'Stripe API', 'JWT'],
                    duration: '3 months',
                    role: 'Full Stack Developer'
                },
                {
                    title: 'Task Management App',
                    description: 'A collaborative task management application with real-time updates, team collaboration features, and advanced analytics.',
                    category: 'web',
                    image: 'assets/images/projects/taskapp.jpg',
                    tags: ['Vue.js', 'Firebase', 'Vuex', 'Socket.io'],
                    featured: true,
                    date: '2023-11-20',
                    githubUrl: 'https://github.com/mohammed-alashrafi/task-manager',
                    liveUrl: 'https://tasks.malashrafi.dev',
                    technologies: ['Vue.js', 'Firebase', 'Vuex', 'Socket.io', 'Chart.js'],
                    duration: '2 months',
                    role: 'Frontend Developer'
                },
                {
                    title: 'Restaurant Mobile App',
                    description: 'A React Native mobile application for restaurant ordering with menu browsing, cart management, and order tracking.',
                    category: 'mobile',
                    image: 'assets/images/projects/restaurant.jpg',
                    tags: ['React Native', 'Redux', 'Firebase', 'Maps API'],
                    featured: true,
                    date: '2023-09-10',
                    githubUrl: 'https://github.com/mohammed-alashrafi/restaurant-app',
                    liveUrl: 'https://play.google.com/store/apps/details?id=com.restaurant.app',
                    technologies: ['React Native', 'Redux', 'Firebase', 'Google Maps API', 'Stripe'],
                    duration: '4 months',
                    role: 'Mobile Developer'
                },
                {
                    title: 'Portfolio Website Template',
                    description: 'A modern, responsive portfolio website template with dark mode, animations, and CMS integration.',
                    category: 'design',
                    image: 'assets/images/projects/portfolio.jpg',
                    tags: ['HTML', 'CSS', 'JavaScript', 'GSAP', 'Figma'],
                    featured: false,
                    date: '2023-07-05',
                    githubUrl: 'https://github.com/mohammed-alashrafi/portfolio-template',
                    liveUrl: 'https://portfolio-template.malashrafi.dev',
                    technologies: ['HTML5', 'CSS3', 'JavaScript', 'GSAP', 'Intersection Observer'],
                    duration: '1 month',
                    role: 'UI/UX Designer & Developer'
                },
                {
                    title: 'Weather Dashboard',
                    description: 'A comprehensive weather dashboard with forecasts, interactive maps, and location-based recommendations.',
                    category: 'web',
                    image: 'assets/images/projects/weather.jpg',
                    tags: ['TypeScript', 'React', 'OpenWeather API', 'Charts.js'],
                    featured: false,
                    date: '2023-05-12',
                    githubUrl: 'https://github.com/mohammed-alashrafi/weather-dashboard',
                    liveUrl: 'https://weather.malashrafi.dev',
                    technologies: ['TypeScript', 'React', 'OpenWeather API', 'Chart.js', 'Leaflet'],
                    duration: '6 weeks',
                    role: 'Frontend Developer'
                },
                {
                    title: 'Fitness Tracker App',
                    description: 'A mobile fitness tracking application with workout planning, progress tracking, and social features.',
                    category: 'mobile',
                    image: 'assets/images/projects/fitness.jpg',
                    tags: ['Flutter', 'Dart', 'Firebase', 'HealthKit'],
                    featured: false,
                    date: '2023-03-08',
                    githubUrl: 'https://github.com/mohammed-alashrafi/fitness-tracker',
                    liveUrl: 'https://apps.apple.com/app/fitness-tracker/id123456789',
                    technologies: ['Flutter', 'Dart', 'Firebase', 'HealthKit', 'Google Fit'],
                    duration: '5 months',
                    role: 'Mobile Developer'
                }
            ];

            for (const project of projects) {
                await this.addData('projects', project);
            }

            // Seed skills
            const skills = [
                { name: 'React/Next.js', category: 'frontend', level: 95 },
                { name: 'TypeScript', category: 'frontend', level: 90 },
                { name: 'Vue.js', category: 'frontend', level: 85 },
                { name: 'CSS/SASS', category: 'frontend', level: 95 },
                { name: 'Node.js', category: 'backend', level: 90 },
                { name: 'Python/Django', category: 'backend', level: 80 },
                { name: 'MongoDB', category: 'backend', level: 85 },
                { name: 'PostgreSQL', category: 'backend', level: 80 },
                { name: 'UI/UX Design', category: 'design', level: 90 },
                { name: 'Figma', category: 'design', level: 85 },
                { name: 'Adobe Creative Suite', category: 'design', level: 80 },
                { name: 'Git/GitHub', category: 'tools', level: 95 }
            ];

            for (const skill of skills) {
                await this.addData('skills', skill);
            }

            // Seed experience
            const experience = [
                {
                    title: 'Senior Web Developer',
                    company: 'TechInnovate Solutions',
                    type: 'work',
                    startDate: '2022-01-01',
                    endDate: null,
                    current: true,
                    description: 'Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern development practices.',
                    technologies: ['React', 'TypeScript', 'Node.js', 'AWS', 'Docker'],
                    achievements: [
                        'Led development of 15+ enterprise applications',
                        'Improved application performance by 40%',
                        'Mentored 5 junior developers',
                        'Implemented CI/CD pipelines'
                    ]
                },
                {
                    title: 'Full Stack Developer',
                    company: 'Digital Dynamics',
                    type: 'work',
                    startDate: '2020-03-01',
                    endDate: '2021-12-31',
                    current: false,
                    description: 'Developed end-to-end web applications using React, Node.js, and MongoDB.',
                    technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'AWS'],
                    achievements: [
                        'Delivered 12 web applications',
                        'Reduced loading times by 50%',
                        'Implemented responsive design principles',
                        'Collaborated with cross-functional teams'
                    ]
                },
                {
                    title: 'Frontend Developer',
                    company: 'WebCraft Studio',
                    type: 'work',
                    startDate: '2019-06-01',
                    endDate: '2020-02-28',
                    current: false,
                    description: 'Specialized in creating responsive websites and web applications.',
                    technologies: ['HTML5', 'CSS3', 'JavaScript', 'jQuery', 'Bootstrap'],
                    achievements: [
                        'Created 20+ responsive websites',
                        'Improved client satisfaction by 95%',
                        'Implemented modern web standards',
                        'Optimized websites for SEO'
                    ]
                },
                {
                    title: 'Bachelor of Computer Science',
                    company: 'University of Technology',
                    type: 'education',
                    startDate: '2016-09-01',
                    endDate: '2020-06-30',
                    current: false,
                    description: 'Graduated with honors, specializing in software engineering and web technologies.',
                    technologies: ['Java', 'C++', 'Python', 'SQL', 'Web Development'],
                    achievements: [
                        'Graduated Summa Cum Laude (GPA: 3.8/4.0)',
                        'Completed capstone project on modern web frameworks',
                        'Active member of programming club',
                        'Organized 3 hackathon events'
                    ]
                }
            ];

            for (const exp of experience) {
                await this.addData('experience', exp);
            }

            // Seed blog posts
            const blogPosts = [
                {
                    title: 'The Future of Web Development: Trends to Watch in 2024',
                    content: 'Exploring the latest trends in web development including AI integration, WebAssembly, and edge computing...',
                    excerpt: 'A comprehensive look at the emerging trends shaping the future of web development.',
                    published: true,
                    date: '2024-01-20',
                    tags: ['Web Development', 'AI', 'WebAssembly', 'Trends'],
                    readTime: 8,
                    author: 'Mohammed Mohammed Al-Ashrafi'
                },
                {
                    title: 'Building Scalable React Applications: Best Practices',
                    content: 'Learn how to build scalable React applications with proper architecture, state management, and performance optimization...',
                    excerpt: 'Essential tips and best practices for building maintainable and scalable React applications.',
                    published: true,
                    date: '2024-01-10',
                    tags: ['React', 'JavaScript', 'Architecture', 'Performance'],
                    readTime: 12,
                    author: 'Mohammed Mohammed Al-Ashrafi'
                },
                {
                    title: 'TypeScript vs JavaScript: When to Choose What',
                    content: 'A detailed comparison of TypeScript and JavaScript, covering use cases, benefits, and decision factors...',
                    excerpt: 'Understanding when to use TypeScript vs JavaScript in your next project.',
                    published: true,
                    date: '2023-12-15',
                    tags: ['TypeScript', 'JavaScript', 'Programming', 'Development'],
                    readTime: 6,
                    author: 'Mohammed Mohammed Al-Ashrafi'
                }
            ];

            for (const post of blogPosts) {
                await this.addData('blogPosts', post);
            }

            console.log('Initial data seeded successfully');
        } catch (error) {
            console.error('Error seeding initial data:', error);
        }
    }

    async addData(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getData(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAllData(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async updateData(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async deleteData(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getCount(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.count();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async saveContactMessage(messageData) {
        const message = {
            ...messageData,
            date: new Date().toISOString(),
            read: false,
            ip: await this.getClientIP()
        };

        return await this.addData('messages', message);
    }

    async trackEvent(eventName, eventData = {}) {
        const event = {
            event: eventName,
            data: eventData,
            date: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: Date.now()
        };

        return await this.addData('analytics', event);
    }

    async getClientIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch (error) {
            console.error('Failed to get client IP:', error);
            return 'unknown';
        }
    }

    async getAnalytics(timeRange = '7d') {
        const events = await this.getAllData('analytics');
        const now = new Date();
        const timeRangeMs = this.getTimeRangeMs(timeRange);
        const filteredEvents = events.filter(event => 
            (now.getTime() - new Date(event.date).getTime()) <= timeRangeMs
        );

        return {
            totalEvents: filteredEvents.length,
            uniqueVisitors: new Set(filteredEvents.map(e => e.data.sessionId)).size,
            topPages: this.getTopPages(filteredEvents),
            eventsByType: this.groupEventsByType(filteredEvents),
            timeSeriesData: this.getTimeSeriesData(filteredEvents, timeRange)
        };
    }

    getTimeRangeMs(timeRange) {
        const ranges = {
            '1d': 24 * 60 * 60 * 1000,
            '7d': 7 * 24 * 60 * 60 * 1000,
            '30d': 30 * 24 * 60 * 60 * 1000,
            '90d': 90 * 24 * 60 * 60 * 1000
        };
        return ranges[timeRange] || ranges['7d'];
    }

    getTopPages(events) {
        const pageViews = {};
        events.forEach(event => {
            if (event.event === 'page_view') {
                pageViews[event.url] = (pageViews[event.url] || 0) + 1;
            }
        });
        
        return Object.entries(pageViews)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .map(([url, count]) => ({ url, count }));
    }

    groupEventsByType(events) {
        const eventTypes = {};
        events.forEach(event => {
            eventTypes[event.event] = (eventTypes[event.event] || 0) + 1;
        });
        return eventTypes;
    }

    getTimeSeriesData(events, timeRange) {
        const data = [];
        const now = new Date();
        const interval = timeRange === '1d' ? 'hour' : 'day';
        
        // Implementation for time series data
        return data;
    }

    // Clean up old data
    async cleanupOldData() {
        try {
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            const analytics = await this.getAllData('analytics');
            
            for (const event of analytics) {
                if (new Date(event.date) < thirtyDaysAgo) {
                    await this.deleteData('analytics', event.id);
                }
            }
            
            console.log('Old analytics data cleaned up');
        } catch (error) {
            console.error('Error cleaning up old data:', error);
        }
    }
}

// Initialize database
const portfolioDb = new PortfolioDatabase();

// Track page load
document.addEventListener('DOMContentLoaded', () => {
    if (portfolioDb.db) {
        portfolioDb.trackEvent('page_view', {
            page: window.location.pathname,
            sessionId: getSessionId()
        });
    }
});

// Generate or get session ID
function getSessionId() {
    let sessionId = sessionStorage.getItem('sessionId');
    if (!sessionId) {
        sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem('sessionId', sessionId);
    }
    return sessionId;
}

// Export for use in other files
window.portfolioDb = portfolioDb;