/* ===== MAIN STYLES ===== */

/* ===== CSS CUSTOM PROPERTIES (VARIABLES) ===== */
:root {
  /* Colors - Perfect Blue Theme */
  --primary-color: #0066ff;
  --secondary-color: #004dcc;
  --accent-color: #0052d9;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
  
  /* Gray Scale */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --black: #000000;
  
  /* Background Colors - Perfect White Theme */
  --bg-primary: #ffffff;
  --bg-secondary: #ffffff;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-500);
  --text-inverse: var(--white);
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
  
  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Breakpoints */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

/* ===== DARK THEME VARIABLES ===== */
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-card: var(--gray-800);
  --bg-overlay: rgba(0, 0, 0, 0.8);
  
  --text-primary: var(--white);
  --text-secondary: var(--gray-300);
  --text-muted: var(--gray-400);
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  overflow-x: hidden;
  transition: all var(--transition-normal);
}

body.loading {
  overflow: hidden;
}

body.nav-open {
  overflow: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin-bottom: var(--space-4);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast);
}

a:hover {
  color: var(--secondary-color);
}

strong {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

em {
  font-style: italic;
}

code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background-color: var(--gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
}

[data-theme="dark"] code {
  background-color: var(--gray-700);
}

/* ===== LAYOUT ===== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.section {
  padding: var(--space-20) 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-weight-extrabold);
  margin-bottom: var(--space-4);
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
}

.section-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background: transparent;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.btn-secondary {
  background: var(--gray-200);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background: var(--gray-300);
}

[data-theme="dark"] .btn-secondary {
  background: var(--gray-700);
  color: var(--text-primary);
}

[data-theme="dark"] .btn-secondary:hover {
  background: var(--gray-600);
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

.btn-small {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

.btn-loading {
  position: relative;
}

.btn-loading .btn-text {
  opacity: 0;
}

.btn-loading .btn-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 1;
}

/* ===== CARDS ===== */
.card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: all var(--transition-slow);
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-6);
}

.loading-logo {
  position: relative;
}

.logo-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  animation: spin 2s linear infinite;
}

.logo-text {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
}

.loading-text {
  text-align: center;
}

.loading-text h2 {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-2);
}

.loading-text p {
  color: var(--text-secondary);
  margin: 0;
}

.loading-progress {
  width: 300px;
  height: 4px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

[data-theme="dark"] .loading-progress {
  background: var(--gray-700);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  animation: loading-progress 2s ease-in-out infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes loading-progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--primary-color);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--secondary-color);
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
}

[data-theme="dark"] .navbar {
  background: rgba(17, 24, 39, 0.95);
  border-bottom-color: var(--gray-700);
}

.navbar.scrolled {
  box-shadow: var(--shadow-lg);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  text-decoration: none;
}

.nav-logo .logo-circle {
  width: 40px;
  height: 40px;
  font-size: var(--text-base);
  background: var(--white);
  color: var(--primary-color);
}

.logo-name {
  display: none;
}

@media (min-width: 768px) {
  .logo-name {
    display: block;
  }
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  color: var(--white);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--white);
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .nav-link:hover,
[data-theme="dark"] .nav-link.active {
  background: var(--gray-800);
}

.nav-link i {
  font-size: var(--text-sm);
}

.nav-link span {
  display: none;
}

@media (min-width: 1024px) {
  .nav-link span {
    display: block;
  }
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.theme-toggle {
  width: 44px;
  height: 44px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  color: var(--white);
}

[data-theme="dark"] .theme-toggle {
  background: var(--gray-800);
}

[data-theme="dark"] .theme-toggle:hover {
  background: var(--gray-700);
}

.hamburger {
  display: none;
  flex-direction: column;
  gap: 4px;
  width: 44px;
  height: 44px;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 8px;
}

.hamburger span {
  width: 100%;
  height: 2px;
  background: var(--white);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

@media (max-width: 1023px) {
  .hamburger {
    display: flex;
  }
  
  .nav-menu {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--bg-card);
    flex-direction: column;
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }
  
  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav-link {
    width: 100%;
    justify-content: center;
    padding: var(--space-4);
  }
  
  .nav-link span {
    display: block;
  }
}

.scroll-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transition: width var(--transition-fast);
  width: 0%;
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: var(--bg-primary);
}

.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  width: 100%;
}

.hero-text {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.greeting {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;
}

.name {
  font-size: var(--text-5xl);
  font-weight: var(--font-weight-extrabold);
  line-height: var(--line-height-tight);
  margin: 0;
  background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.name .accent {
  color: var(--primary-color);
}

.title-accent {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-medium);
  color: var(--secondary-color);
  margin: 0;
}

.hero-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  max-width: 500px;
  margin: 0;
}

.hero-cta {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.hero-social {
  display: flex;
  gap: var(--space-4);
}

.social-link {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-xl);
  background: var(--gray-100);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  transform: translateY(-3px);
}

[data-theme="dark"] .social-link {
  background: var(--gray-800);
  color: var(--gray-300);
}

.hero-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-container {
  position: relative;
  width: 300px;
  height: 300px;
}

.avatar-ring {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 3px solid transparent;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) border-box;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: rotate 10s linear infinite;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--white);
  box-shadow: var(--shadow-xl);
}

.avatar-status {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: var(--success-color);
  color: var(--white);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-lg);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: var(--white);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.hero-scroll {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-secondary);
  z-index: 2;
}

.scroll-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.scroll-indicator:hover {
  color: var(--primary-color);
}

.scroll-indicator i {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* ===== ABOUT SECTION ===== */
.about {
  background: var(--bg-primary);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  margin-bottom: var(--space-16);
}

.about-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-6);
  color: var(--text-primary);
}

.about-description {
  font-size: var(--text-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
}

.about-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.detail-item {
  display: flex;
  gap: var(--space-4);
}

.detail-item strong {
  min-width: 100px;
  color: var(--text-primary);
}

.detail-item span {
  color: var(--text-secondary);
  flex: 1;
}

.about-actions {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.about-visual {
  display: flex;
  justify-content: center;
}

.about-img-container {
  position: relative;
  width: 400px;
  height: 500px;
  border-radius: var(--radius-2xl);
  overflow: hidden;
}

.about-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.about-image:hover {
  transform: scale(1.05);
}

.img-decoration {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 3px solid var(--primary-color);
  border-radius: var(--radius-2xl);
  z-index: -1;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-8);
  text-align: center;
}

.stat-item {
  padding: var(--space-6);
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.stat-number {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--primary-color);
  margin-bottom: var(--space-2);
}

.stat-label {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: var(--text-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all var(--transition-normal);
  z-index: var(--z-fixed);
  box-shadow: var(--shadow-lg);
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

/* ===== NOTIFICATIONS ===== */
.notification {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--space-4);
  max-width: 400px;
  z-index: var(--z-toast);
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--transition-normal);
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification.hide {
  transform: translateX(100%);
  opacity: 0;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.notification-icon {
  font-size: var(--text-lg);
}

.notification-success .notification-icon {
  color: var(--success-color);
}

.notification-error .notification-icon {
  color: var(--error-color);
}

.notification-warning .notification-icon {
  color: var(--warning-color);
}

.notification-info .notification-icon {
  color: var(--info-color);
}

.notification-message {
  flex: 1;
  color: var(--text-primary);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: var(--text-lg);
  padding: var(--space-1);
}

.notification-close:hover {
  color: var(--text-primary);
}

/* ===== UTILITY CLASSES ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-card { background-color: var(--bg-card); }

.mb-0 { margin-bottom: 0; }
.mb-4 { margin-bottom: var(--space-4); }
.mb-8 { margin-bottom: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-4 { margin-top: var(--space-4); }
.mt-8 { margin-top: var(--space-8); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.w-full { width: 100%; }
.h-full { height: 100%; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.cursor-pointer { cursor: pointer; }
.pointer-events-none { pointer-events: none; }

.select-none { user-select: none; }

.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.scale-110 { transform: scale(1.1); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--white);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 100;
}

.skip-link:focus {
  top: 6px;
}

/* Focus styles for keyboard navigation */
.keyboard-navigation *:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0066cc;
    --secondary-color: #cc0066;
  }
}