<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON> - Full Stack Developer & UI/UX Designer specializing in modern web technologies and user-centered design.">
    <meta name="keywords" content="<PERSON>, Full Stack Developer, Web Developer, UI/UX Designer, React, Node.js, JavaScript, Portfolio">
    <meta name="author" content="Mohammed <PERSON>Ash<PERSON>fi">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Mohammed <PERSON>rafi - Full Stack Developer">
    <meta property="og:description" content="Full Stack Developer & UI/UX Designer specializing in modern web technologies and user-centered design.">
    <meta property="og:image" content="assets/images/og-image.jpg">
    <meta property="og:url" content="https://malashrafi.dev">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Mohammed <PERSON> Portfolio">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Mohammed Mohammed Al-Ashrafi - Full Stack Developer">
    <meta name="twitter:description" content="Full Stack Developer & UI/UX Designer specializing in modern web technologies and user-centered design.">
    <meta name="twitter:image" content="assets/images/twitter-card.jpg">
    <meta name="twitter:creator" content="@malashrafi">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="manifest" href="assets/images/site.webmanifest">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <!-- Theme Color -->
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-TileColor" content="#667eea">
    
    <title>Mohammed Mohammed Al-Ashrafi - Full Stack Developer & Designer</title>
</head>

<body class="loading" data-theme="light">
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="logo-circle">
                    <span class="logo-text">M</span>
                </div>
            </div>
            <div class="loading-text">
                <h2>Mohammed Al-Ashrafi</h2>
                <p>Loading Portfolio...</p>
            </div>
            <div class="loading-progress">
                <div class="progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav id="navbar" class="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="#home" class="nav-logo">
                    <div class="logo-circle">
                        <span class="logo-text">M</span>
                    </div>
                    <span class="logo-name">Mohammed Al-Ashrafi</span>
                </a>
                
                <div id="navMenu" class="nav-menu">
                    <a href="#home" class="nav-link active">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </a>
                    <a href="#about" class="nav-link">
                        <i class="fas fa-user"></i>
                        <span>About</span>
                    </a>
                    <a href="#skills" class="nav-link">
                        <i class="fas fa-code"></i>
                        <span>Skills</span>
                    </a>
                    <a href="#portfolio" class="nav-link">
                        <i class="fas fa-briefcase"></i>
                        <span>Portfolio</span>
                    </a>
                    <a href="#experience" class="nav-link">
                        <i class="fas fa-history"></i>
                        <span>Experience</span>
                    </a>
                    <a href="#services" class="nav-link">
                        <i class="fas fa-concierge-bell"></i>
                        <span>Services</span>
                    </a>
                    <a href="#cv" class="nav-link">
                        <i class="fas fa-file-alt"></i>
                        <span>CV</span>
                    </a>
                    <a href="#contact" class="nav-link">
                        <i class="fas fa-envelope"></i>
                        <span>Contact</span>
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button id="themeToggle" class="theme-toggle" aria-label="Toggle theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button id="hamburger" class="hamburger">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </div>
        <div class="scroll-progress"></div>
    </nav>

    <!-- Main Content -->
    <main id="main">
        <!-- Hero Section -->
        <section id="home" class="hero">
        <div class="hero-particles"></div>
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <p class="greeting fade-in">Hello, I'm</p>
                    <h1 class="name fade-in">Mohammed Mohammed<br><span class="accent">Al-Ashrafi</span></h1>
                    <h2 class="title-accent fade-in">Full Stack Developer & Designer</h2>
                    <p class="hero-description fade-in">
                        I create exceptional digital experiences through innovative web development 
                        and user-centered design. Passionate about turning ideas into beautiful, 
                        functional applications.
                    </p>
                    <div class="hero-cta fade-in">
                        <a href="#portfolio" class="btn btn-primary">
                            <span class="btn-text">View My Work</span>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                        <a href="#contact" class="btn btn-outline">
                            <span class="btn-text">Get In Touch</span>
                            <i class="fas fa-paper-plane"></i>
                        </a>
                    </div>
                    <div class="hero-social fade-in">
                        <a href="https://github.com/mohammed-alashrafi" class="social-link" target="_blank" rel="noopener" aria-label="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="https://linkedin.com/in/mohammed-alashrafi" class="social-link" target="_blank" rel="noopener" aria-label="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="https://twitter.com/malashrafi" class="social-link" target="_blank" rel="noopener" aria-label="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://instagram.com/malashrafi" class="social-link" target="_blank" rel="noopener" aria-label="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
                <div class="hero-avatar scale-in">
                    <div class="avatar-container">
                        <div class="avatar-ring"></div>
                        <img src="assets/images/mohammed-photo.jpg" alt="Mohammed Mohammed Al-Ashrafi" class="avatar-image">
                        <div class="avatar-status">
                            <div class="status-dot"></div>
                            <span class="status-text">Available for work</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="hero-scroll">
            <div class="scroll-indicator">
                <span>Scroll Down</span>
                <i class="fas fa-arrow-down"></i>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section about">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">About Me</h2>
                <p class="section-subtitle">Get to know me better</p>
            </div>
            
            <div class="about-content">
                <div class="about-text fade-in-left">
                    <h3 class="about-title">I'm Mohammed, a passionate developer and designer</h3>
                    <p class="about-description">
                        With over 5 years of experience in web development and design, I specialize in creating 
                        modern, responsive applications that deliver exceptional user experiences. My journey began 
                        with a curiosity for how things work on the web, and it has evolved into a passion for 
                        crafting digital solutions that make a difference.
                    </p>
                    <p class="about-description">
                        I believe in the power of clean code, intuitive design, and continuous learning. When I'm 
                        not coding, you can find me exploring new technologies, contributing to open-source projects, 
                        or sharing knowledge with the developer community.
                    </p>
                    
                    <div class="about-details">
                        <div class="detail-item">
                            <strong>Name:</strong>
                            <span>Mohammed Mohammed Al-Ashrafi</span>
                        </div>
                        <div class="detail-item">
                            <strong>Email:</strong>
                            <span><EMAIL></span>
                        </div>
                        <div class="detail-item">
                            <strong>Location:</strong>
                            <span>Riyadh, Saudi Arabia</span>
                        </div>
                        <div class="detail-item">
                            <strong>Languages:</strong>
                            <span>Arabic (Native), English (Fluent)</span>
                        </div>
                    </div>
                    
                    <div class="about-actions">
                        <a href="#cv" class="btn btn-primary">
                            <i class="fas fa-file-alt"></i>
                            <span class="btn-text">View CV</span>
                        </a>
                        <a href="assets/documents/Mohammed-Al-Ashrafi-Resume.pdf" class="btn btn-outline" download>
                            <i class="fas fa-download"></i>
                            <span class="btn-text">Download CV</span>
                        </a>
                        <a href="#contact" class="btn btn-outline">
                            <i class="fas fa-envelope"></i>
                            <span class="btn-text">Hire Me</span>
                        </a>
                    </div>
                </div>
                
                <div class="about-visual fade-in-right">
                    <div class="about-img-container">
                        <img src="assets/images/placeholder.svg" alt="Mohammed Al-Ashrafi working" class="about-image">
                        <div class="img-decoration"></div>
                    </div>
                </div>
            </div>
            
            <div class="about-stats fade-in">
                <div class="stat-item">
                    <div class="stat-number" data-count="50">0</div>
                    <div class="stat-label">Projects Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" data-count="30">0</div>
                    <div class="stat-label">Happy Clients</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" data-count="5">0</div>
                    <div class="stat-label">Years Experience</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" data-count="100">0</div>
                    <div class="stat-label">Cups of Coffee</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="section skills">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">My Skills</h2>
                <p class="section-subtitle">Technologies I work with</p>
            </div>
            
            <div class="skills-categories">
                <div class="skill-category fade-in stagger-item">
                    <h3 class="category-title">Frontend Development</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">React/Next.js</span>
                                <span class="skill-percentage">95%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="95"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">TypeScript</span>
                                <span class="skill-percentage">90%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Vue.js</span>
                                <span class="skill-percentage">85%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">CSS/SASS</span>
                                <span class="skill-percentage">95%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="95"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category fade-in stagger-item">
                    <h3 class="category-title">Backend Development</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Node.js</span>
                                <span class="skill-percentage">90%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Python/Django</span>
                                <span class="skill-percentage">80%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="80"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">MongoDB</span>
                                <span class="skill-percentage">85%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">PostgreSQL</span>
                                <span class="skill-percentage">80%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="80"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="skill-category fade-in stagger-item">
                    <h3 class="category-title">Design & Tools</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">UI/UX Design</span>
                                <span class="skill-percentage">90%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Figma</span>
                                <span class="skill-percentage">85%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Adobe Creative Suite</span>
                                <span class="skill-percentage">80%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="80"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">Git/GitHub</span>
                                <span class="skill-percentage">95%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="95"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="section portfolio">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">My Portfolio</h2>
                <p class="section-subtitle">Recent projects I've worked on</p>
            </div>
            
            <div class="portfolio-filters fade-in">
                <button class="filter-btn active" data-filter="all">All Projects</button>
                <button class="filter-btn" data-filter="web">Web Apps</button>
                <button class="filter-btn" data-filter="mobile">Mobile Apps</button>
                <button class="filter-btn" data-filter="design">Design</button>
            </div>
            
            <div id="portfolioGrid" class="portfolio-grid">
                <!-- Projects will be loaded dynamically -->
            </div>
            
            <div class="portfolio-load-more">
                <button id="loadMoreBtn" class="btn btn-outline" style="display: none;">
                    Load More Projects
                </button>
            </div>
        </div>
    </section>

    <!-- Experience Section -->
    <section id="experience" class="section experience">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Experience</h2>
                <p class="section-subtitle">My professional journey</p>
            </div>
            
            <div class="experience-timeline">
                <div class="timeline-item fade-in">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <span class="timeline-date">2022 - Present</span>
                        <h3 class="timeline-title">Senior Web Developer</h3>
                        <h4 class="timeline-company">TechInnovate Solutions</h4>
                        <p class="timeline-description">
                            Leading frontend development for enterprise applications, mentoring junior developers, 
                            and implementing modern development practices. Successfully delivered 15+ projects with 
                            improved performance and user satisfaction.
                        </p>
                    </div>
                </div>
                
                <div class="timeline-item fade-in">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <span class="timeline-date">2020 - 2021</span>
                        <h3 class="timeline-title">Full Stack Developer</h3>
                        <h4 class="timeline-company">Digital Dynamics</h4>
                        <p class="timeline-description">
                            Developed end-to-end web applications using React, Node.js, and MongoDB. 
                            Collaborated with cross-functional teams to deliver high-quality solutions 
                            that exceeded client expectations.
                        </p>
                    </div>
                </div>
                
                <div class="timeline-item fade-in">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <span class="timeline-date">2019 - 2020</span>
                        <h3 class="timeline-title">Frontend Developer</h3>
                        <h4 class="timeline-company">WebCraft Studio</h4>
                        <p class="timeline-description">
                            Specialized in creating responsive websites and web applications. 
                            Implemented modern web standards and optimized applications for 
                            performance and accessibility.
                        </p>
                    </div>
                </div>
                
                <div class="timeline-item fade-in">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <span class="timeline-date">2016 - 2020</span>
                        <h3 class="timeline-title">Bachelor of Computer Science</h3>
                        <h4 class="timeline-company">University of Technology</h4>
                        <p class="timeline-description">
                            Graduated with honors, specializing in software engineering and web technologies. 
                            Completed capstone project on modern web frameworks and participated in 
                            multiple hackathon events.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="section services">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Services</h2>
                <p class="section-subtitle">What I offer</p>
            </div>
            
            <div class="services-grid">
                <div class="service-card fade-in stagger-item">
                    <div class="service-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="service-title">Web Development</h3>
                    <p class="service-description">
                        Custom web applications built with modern technologies, 
                        ensuring performance, scalability, and user experience.
                    </p>
                    <ul class="service-features">
                        <li>React/Vue.js Applications</li>
                        <li>Node.js Backend Development</li>
                        <li>Database Design & Integration</li>
                        <li>API Development & Integration</li>
                        <li>Performance Optimization</li>
                    </ul>
                </div>
                
                <div class="service-card fade-in stagger-item">
                    <div class="service-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="service-title">Mobile Development</h3>
                    <p class="service-description">
                        Cross-platform mobile applications using React Native and Flutter, 
                        delivering native performance across iOS and Android.
                    </p>
                    <ul class="service-features">
                        <li>React Native Development</li>
                        <li>Flutter Applications</li>
                        <li>App Store Deployment</li>
                        <li>Push Notifications</li>
                        <li>Offline Functionality</li>
                    </ul>
                </div>
                
                <div class="service-card fade-in stagger-item">
                    <div class="service-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <h3 class="service-title">UI/UX Design</h3>
                    <p class="service-description">
                        User-centered design solutions that combine aesthetics with functionality, 
                        creating intuitive and engaging digital experiences.
                    </p>
                    <ul class="service-features">
                        <li>User Research & Analysis</li>
                        <li>Wireframing & Prototyping</li>
                        <li>Visual Design & Branding</li>
                        <li>Usability Testing</li>
                        <li>Design Systems</li>
                    </ul>
                </div>
                
                <div class="service-card fade-in stagger-item">
                    <div class="service-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3 class="service-title">Consulting</h3>
                    <p class="service-description">
                        Technical consultation and project planning to help businesses 
                        make informed decisions about their digital transformation.
                    </p>
                    <ul class="service-features">
                        <li>Technology Stack Selection</li>
                        <li>Architecture Planning</li>
                        <li>Code Review & Audit</li>
                        <li>Performance Analysis</li>
                        <li>Team Mentoring</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- CV Section -->
    <section id="cv" class="section cv">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title fade-in">Curriculum Vitae</h2>
                <p class="section-subtitle fade-in">A comprehensive overview of my professional background</p>
            </div>
            
            <div class="cv-content">
                <!-- CV Header -->
                <div class="cv-header fade-in">
                    <div class="cv-personal">
                        <h1 class="cv-name">Mohammed Mohammed Al-Ashrafi</h1>
                        <div class="cv-title">Full Stack Developer & UI/UX Designer</div>
                        <div class="cv-contact-info">
                            <div class="cv-contact-item">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="cv-contact-item">
                                <i class="fas fa-phone"></i>
                                <span>+****************</span>
                            </div>
                            <div class="cv-contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>New York, NY</span>
                            </div>
                            <div class="cv-contact-item">
                                <i class="fab fa-linkedin"></i>
                                <span>linkedin.com/in/mohammed-alashrafi</span>
                            </div>
                            <div class="cv-contact-item">
                                <i class="fab fa-github"></i>
                                <span>github.com/mohammed-alashrafi</span>
                            </div>
                        </div>
                    </div>
                    <div class="cv-avatar">
                        <img src="assets/images/placeholder.svg" alt="Mohammed Al-Ashrafi" class="cv-photo">
                    </div>
                </div>

                <!-- Professional Summary -->
                <div class="cv-section fade-in">
                    <h2 class="cv-section-title">Professional Summary</h2>
                    <div class="cv-section-content">
                        <p>Experienced Full Stack Developer with 5+ years of expertise in modern web technologies including React, Node.js, and cloud platforms. Passionate about creating user-centered designs and scalable applications. Proven track record of delivering high-quality solutions for diverse clients ranging from startups to enterprise companies.</p>
                    </div>
                </div>

                <!-- Work Experience -->
                <div class="cv-section fade-in">
                    <h2 class="cv-section-title">Work Experience</h2>
                    <div class="cv-section-content">
                        <div class="cv-experience-item">
                            <div class="cv-experience-header">
                                <h3 class="cv-job-title">Senior Full Stack Developer</h3>
                                <div class="cv-company">TechInnovate Solutions</div>
                                <div class="cv-date">January 2022 - Present</div>
                            </div>
                            <div class="cv-job-description">
                                <p>Lead development of enterprise-level web applications using React, TypeScript, and Node.js. Manage a team of 3 junior developers and collaborate with cross-functional teams to deliver scalable solutions.</p>
                                <ul class="cv-achievements">
                                    <li>Increased application performance by 40% through code optimization and implementation of best practices</li>
                                    <li>Led the development of 15+ enterprise applications serving 10,000+ users</li>
                                    <li>Implemented CI/CD pipelines reducing deployment time by 60%</li>
                                    <li>Mentored 5 junior developers, improving team productivity by 30%</li>
                                </ul>
                            </div>
                        </div>

                        <div class="cv-experience-item">
                            <div class="cv-experience-header">
                                <h3 class="cv-job-title">Full Stack Developer</h3>
                                <div class="cv-company">Digital Dynamics</div>
                                <div class="cv-date">March 2020 - December 2021</div>
                            </div>
                            <div class="cv-job-description">
                                <p>Developed end-to-end web applications using React, Node.js, and MongoDB. Collaborated with design teams to implement responsive and user-friendly interfaces.</p>
                                <ul class="cv-achievements">
                                    <li>Built and deployed 12+ web applications with 99.9% uptime</li>
                                    <li>Reduced page loading times by 50% through performance optimization</li>
                                    <li>Implemented responsive design principles across all projects</li>
                                    <li>Collaborated with UX/UI designers to improve user experience</li>
                                </ul>
                            </div>
                        </div>

                        <div class="cv-experience-item">
                            <div class="cv-experience-header">
                                <h3 class="cv-job-title">Frontend Developer</h3>
                                <div class="cv-company">WebCraft Studio</div>
                                <div class="cv-date">June 2019 - February 2020</div>
                            </div>
                            <div class="cv-job-description">
                                <p>Specialized in creating responsive websites and web applications using modern frontend technologies. Focused on performance optimization and cross-browser compatibility.</p>
                                <ul class="cv-achievements">
                                    <li>Created 20+ responsive websites with mobile-first approach</li>
                                    <li>Achieved 95% client satisfaction rate</li>
                                    <li>Implemented modern web standards and SEO best practices</li>
                                    <li>Reduced development time by 25% through component reusability</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Education -->
                <div class="cv-section fade-in">
                    <h2 class="cv-section-title">Education</h2>
                    <div class="cv-section-content">
                        <div class="cv-education-item">
                            <div class="cv-education-header">
                                <h3 class="cv-degree">Bachelor of Computer Science</h3>
                                <div class="cv-school">University of Technology</div>
                                <div class="cv-date">September 2016 - June 2020</div>
                            </div>
                            <div class="cv-education-details">
                                <p><strong>GPA:</strong> 3.8/4.0 (Summa Cum Laude)</p>
                                <p><strong>Relevant Coursework:</strong> Software Engineering, Web Development, Database Management, Algorithms & Data Structures, Human-Computer Interaction</p>
                                <p><strong>Senior Project:</strong> Real-time Collaborative Task Management Platform using React and WebSocket technology</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technical Skills -->
                <div class="cv-section fade-in">
                    <h2 class="cv-section-title">Technical Skills</h2>
                    <div class="cv-section-content">
                        <div class="cv-skills-grid">
                            <div class="cv-skill-category">
                                <h4>Frontend Development</h4>
                                <div class="cv-skill-tags">
                                    <span class="cv-skill-tag">React/Next.js</span>
                                    <span class="cv-skill-tag">Vue.js/Nuxt.js</span>
                                    <span class="cv-skill-tag">TypeScript</span>
                                    <span class="cv-skill-tag">JavaScript (ES6+)</span>
                                    <span class="cv-skill-tag">HTML5/CSS3</span>
                                    <span class="cv-skill-tag">SASS/SCSS</span>
                                    <span class="cv-skill-tag">Tailwind CSS</span>
                                    <span class="cv-skill-tag">Bootstrap</span>
                                </div>
                            </div>
                            <div class="cv-skill-category">
                                <h4>Backend Development</h4>
                                <div class="cv-skill-tags">
                                    <span class="cv-skill-tag">Node.js/Express</span>
                                    <span class="cv-skill-tag">Python/Django</span>
                                    <span class="cv-skill-tag">RESTful APIs</span>
                                    <span class="cv-skill-tag">GraphQL</span>
                                    <span class="cv-skill-tag">Microservices</span>
                                    <span class="cv-skill-tag">WebSocket</span>
                                </div>
                            </div>
                            <div class="cv-skill-category">
                                <h4>Database & Cloud</h4>
                                <div class="cv-skill-tags">
                                    <span class="cv-skill-tag">MongoDB</span>
                                    <span class="cv-skill-tag">PostgreSQL</span>
                                    <span class="cv-skill-tag">Redis</span>
                                    <span class="cv-skill-tag">AWS</span>
                                    <span class="cv-skill-tag">Docker</span>
                                    <span class="cv-skill-tag">Kubernetes</span>
                                </div>
                            </div>
                            <div class="cv-skill-category">
                                <h4>Tools & Others</h4>
                                <div class="cv-skill-tags">
                                    <span class="cv-skill-tag">Git/GitHub</span>
                                    <span class="cv-skill-tag">Jest/Testing</span>
                                    <span class="cv-skill-tag">Webpack/Vite</span>
                                    <span class="cv-skill-tag">Figma</span>
                                    <span class="cv-skill-tag">Adobe Creative Suite</span>
                                    <span class="cv-skill-tag">Agile/Scrum</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Certifications -->
                <div class="cv-section fade-in">
                    <h2 class="cv-section-title">Certifications</h2>
                    <div class="cv-section-content">
                        <div class="cv-certifications">
                            <div class="cv-cert-item">
                                <h4>AWS Certified Developer - Associate</h4>
                                <p>Amazon Web Services • 2023</p>
                            </div>
                            <div class="cv-cert-item">
                                <h4>Google Cloud Professional Developer</h4>
                                <p>Google Cloud • 2022</p>
                            </div>
                            <div class="cv-cert-item">
                                <h4>React Developer Certification</h4>
                                <p>Meta (Facebook) • 2022</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Languages -->
                <div class="cv-section fade-in">
                    <h2 class="cv-section-title">Languages</h2>
                    <div class="cv-section-content">
                        <div class="cv-languages">
                            <div class="cv-language-item">
                                <span class="cv-language-name">Arabic</span>
                                <span class="cv-language-level">Native</span>
                            </div>
                            <div class="cv-language-item">
                                <span class="cv-language-name">English</span>
                                <span class="cv-language-level">Fluent</span>
                            </div>
                            <div class="cv-language-item">
                                <span class="cv-language-name">French</span>
                                <span class="cv-language-level">Intermediate</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CV Actions -->
                <div class="cv-actions fade-in">
                    <a href="assets/documents/Mohammed-Al-Ashrafi-Resume.pdf" class="btn btn-primary" download>
                        <i class="fas fa-download"></i>
                        <span class="btn-text">Download PDF</span>
                    </a>
                    <button class="btn btn-outline" onclick="window.print()">
                        <i class="fas fa-print"></i>
                        <span class="btn-text">Print CV</span>
                    </button>
                    <a href="#contact" class="btn btn-outline">
                        <i class="fas fa-envelope"></i>
                        <span class="btn-text">Contact Me</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section contact">
        <div class="container">
            <div class="section-header fade-in">
                <h2 class="section-title">Get In Touch</h2>
                <p class="section-subtitle">Let's work together</p>
            </div>
            
            <div class="contact-content">
                <div class="contact-info fade-in-left">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Email</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Phone</h3>
                            <p>+966 50 123 4567</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h3>Location</h3>
                            <p>Riyadh, Saudi Arabia</p>
                        </div>
                    </div>
                    
                    <div class="contact-social">
                        <h3>Follow Me</h3>
                        <div class="social-links">
                            <a href="https://github.com/mohammed-alashrafi" class="social-link" target="_blank" rel="noopener" aria-label="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="https://linkedin.com/in/mohammed-alashrafi" class="social-link" target="_blank" rel="noopener" aria-label="LinkedIn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="https://twitter.com/malashrafi" class="social-link" target="_blank" rel="noopener" aria-label="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://instagram.com/malashrafi" class="social-link" target="_blank" rel="noopener" aria-label="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <form id="contactForm" class="contact-form fade-in-right">
                    <div class="form-group">
                        <label for="name">Name *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="subject">Subject *</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Message *</label>
                        <textarea id="message" name="message" rows="6" required></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <span class="btn-text">Send Message</span>
                        <span class="btn-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </section>

    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2024 Mohammed Mohammed Al-Ashrafi. All rights reserved.</p>
                    <p>Built with ❤️ using modern web technologies</p>
                </div>
                <div class="footer-links">
                    <a href="#" class="footer-link">Privacy Policy</a>
                    <a href="#" class="footer-link">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript Files -->
    <script src="js/database.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/portfolio.js"></script>
    <script src="js/main.js"></script>
</body>
</html>