// ===== MAIN APPLICATION =====

class PortfolioApp {
    constructor() {
        this.isLoaded = false;
        this.currentSection = 'home';
        this.init();
    }

    async init() {
        try {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeApp());
            } else {
                this.initializeApp();
            }
        } catch (error) {
            console.error('Error initializing app:', error);
            this.handleError(error);
        }
    }

    async initializeApp() {
        try {
            // Show loading screen
            this.showLoading();

            // Initialize core components
            await this.initializeDatabase();
            this.initializeUI();
            this.initializeEventListeners();
            this.initializeAnimations();
            this.setupAnalytics();
            this.setupPerformanceMonitoring();
            this.setupAccessibility();

            // Load dynamic content
            await this.loadContent();

            // Setup service worker
            this.setupServiceWorker();

            // Hide loading screen
            this.hideLoading();

            // Mark as loaded
            this.isLoaded = true;

            console.log('Portfolio app initialized successfully');
        } catch (error) {
            console.error('Error during app initialization:', error);
            this.handleError(error);
        }
    }

    showLoading() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
        document.body.classList.add('loading');
    }

    hideLoading() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                document.body.classList.remove('loading');
                
                setTimeout(() => {
                    loadingScreen.remove();
                }, 500);
            }, 1000);
        }
    }

    async initializeDatabase() {
        // Database is already initialized in database.js
        // Just wait for it to be ready
        return new Promise((resolve) => {
            const checkDatabase = () => {
                if (window.portfolioDb && window.portfolioDb.db) {
                    resolve();
                } else {
                    setTimeout(checkDatabase, 100);
                }
            };
            checkDatabase();
        });
    }

    initializeUI() {
        // Initialize theme
        this.initializeTheme();
        
        // Initialize navigation
        this.initializeNavigation();
        
        // Initialize forms
        this.initializeForms();
        
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize modals
        this.initializeModals();
    }

    initializeTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = savedTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
            }
        }
    }

    initializeNavigation() {
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = anchor.getAttribute('href').substring(1);
                this.scrollToSection(targetId);
            });
        });

        // Update active navigation on scroll
        this.updateActiveNavigation();
    }

    initializeForms() {
        const contactForm = document.getElementById('contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => this.handleContactForm(e));
        }
    }

    initializeTooltips() {
        // Add tooltips to elements with title attributes
        document.querySelectorAll('[title]').forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    }

    initializeModals() {
        // Initialize modal functionality
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-backdrop') || 
                e.target.classList.contains('modal-close')) {
                this.closeModal(e.target.closest('.modal, .project-modal, .post-modal'));
            }
        });

        // Close modals on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show, .project-modal.show, .post-modal.show');
                if (openModal) {
                    this.closeModal(openModal);
                }
            }
        });
    }

    initializeEventListeners() {
        // Window resize handler
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Scroll handler
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, 16));

        // Visibility change handler
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // Error handlers
        window.addEventListener('error', (e) => {
            this.handleError(e.error);
        });

        window.addEventListener('unhandledrejection', (e) => {
            this.handleError(e.reason);
        });
    }

    initializeAnimations() {
        // Initialize intersection observer for animations
        if ('IntersectionObserver' in window) {
            this.animationObserver = new IntersectionObserver(
                this.handleAnimationIntersection.bind(this),
                { threshold: 0.1, rootMargin: '50px' }
            );

            // Observe elements with animation classes
            document.querySelectorAll('.fade-in, .slide-in, .scale-in').forEach(el => {
                this.animationObserver.observe(el);
            });
        }
    }

    setupAnalytics() {
        // Track page views
        this.trackPageView();
        
        // Track user interactions
        this.setupInteractionTracking();
        
        // Track performance metrics
        this.trackPerformanceMetrics();
    }

    setupPerformanceMonitoring() {
        // Monitor Core Web Vitals
        this.monitorWebVitals();
        
        // Monitor resource loading
        this.monitorResourceLoading();
        
        // Monitor JavaScript errors
        this.monitorErrors();
    }

    setupAccessibility() {
        // Skip links
        this.setupSkipLinks();
        
        // Focus management
        this.setupFocusManagement();
        
        // Keyboard navigation
        this.setupKeyboardNavigation();
        
        // Screen reader support
        this.setupScreenReaderSupport();
    }

    setupInteractionTracking() {
        // Track clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('a, button')) {
                this.trackEvent('click', {
                    element: e.target.tagName,
                    text: e.target.textContent.slice(0, 50)
                });
            }
        });
    }

    trackPerformanceMetrics() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    this.trackEvent('performance', {
                        loadTime: perfData.loadEventEnd - perfData.fetchStart,
                        domComplete: perfData.domComplete - perfData.fetchStart
                    });
                }
            });
        }
    }

    monitorWebVitals() {
        // Simple web vitals monitoring
        if ('performance' in window && 'PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    list.getEntries().forEach((entry) => {
                        if (entry.entryType === 'measure') {
                            this.trackEvent('web_vital', {
                                name: entry.name,
                                value: entry.duration
                            });
                        }
                    });
                });
                observer.observe({ entryTypes: ['measure'] });
            } catch (e) {
                console.log('Performance observer not supported');
            }
        }
    }

    monitorResourceLoading() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                const resources = performance.getEntriesByType('resource');
                const slowResources = resources.filter(r => r.duration > 1000);
                if (slowResources.length > 0) {
                    this.trackEvent('slow_resources', {
                        count: slowResources.length,
                        resources: slowResources.map(r => r.name)
                    });
                }
            });
        }
    }

    monitorErrors() {
        // Already handled in initializeEventListeners
    }

    setupSkipLinks() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main';
        skipLink.className = 'skip-link';
        skipLink.textContent = 'Skip to main content';
        skipLink.style.cssText = `
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color);
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
        `;
        
        skipLink.addEventListener('focus', () => {
            skipLink.style.top = '6px';
        });
        
        skipLink.addEventListener('blur', () => {
            skipLink.style.top = '-40px';
        });
        
        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    setupFocusManagement() {
        // Add focus indicators
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('using-keyboard');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('using-keyboard');
        });
    }

    setupKeyboardNavigation() {
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const modal = document.querySelector('.modal.show, .project-modal.show');
                if (modal) {
                    this.closeModal(modal);
                }
            }
        });
    }

    setupScreenReaderSupport() {
        // Add aria-labels where needed
        document.querySelectorAll('button:not([aria-label])').forEach(button => {
            if (!button.textContent.trim()) {
                const icon = button.querySelector('i');
                if (icon && icon.className.includes('fa-')) {
                    button.setAttribute('aria-label', this.getButtonLabel(icon.className));
                }
            }
        });
    }

    getButtonLabel(iconClass) {
        const labels = {
            'fa-menu': 'Menu',
            'fa-close': 'Close',
            'fa-times': 'Close',
            'fa-moon': 'Dark mode',
            'fa-sun': 'Light mode',
            'fa-arrow-up': 'Back to top'
        };

        for (const [icon, label] of Object.entries(labels)) {
            if (iconClass.includes(icon)) {
                return label;
            }
        }
        return 'Button';
    }

    async loadContent() {
        try {
            // Load dynamic content
            await this.loadProjects();
            await this.loadSkills();
            await this.loadExperience();
            await this.loadBlogPosts();
            
            // Initialize content-specific features
            this.initializeContentFeatures();
        } catch (error) {
            console.error('Error loading content:', error);
            this.showNotification('Some content failed to load', 'warning');
        }
    }

    initializeContentFeatures() {
        // Initialize any content-specific features
        this.initializeImageLazyLoading();
        this.initializeScrollAnimations();
    }

    initializeImageLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        if ('IntersectionObserver' in window && images.length > 0) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }
    }

    initializeScrollAnimations() {
        // Already handled in initializeAnimations
    }

    async loadProjects() {
        try {
            if (window.portfolioDb) {
                const projects = await window.portfolioDb.getAllData('projects');
                this.renderProjects(projects);
            }
        } catch (error) {
            console.error('Error loading projects:', error);
        }
    }

    async loadSkills() {
        try {
            if (window.portfolioDb) {
                const skills = await window.portfolioDb.getAllData('skills');
                this.renderSkills(skills);
            }
        } catch (error) {
            console.error('Error loading skills:', error);
        }
    }

    async loadExperience() {
        try {
            if (window.portfolioDb) {
                const experience = await window.portfolioDb.getAllData('experience');
                this.renderExperience(experience);
            }
        } catch (error) {
            console.error('Error loading experience:', error);
        }
    }

    async loadBlogPosts() {
        try {
            if (window.portfolioDb) {
                const posts = await window.portfolioDb.getByIndex('blogPosts', 'published', true);
                this.renderBlogPosts(posts);
            }
        } catch (error) {
            console.error('Error loading blog posts:', error);
        }
    }

    renderProjects(projects) {
        // This will be handled by PortfolioManager
        console.log('Projects loaded:', projects.length);
    }

    renderSkills(skills) {
        const skillsContainer = document.querySelector('.skills-list');
        if (skillsContainer && skills.length > 0) {
            // Group skills by category
            const skillsByCategory = skills.reduce((acc, skill) => {
                if (!acc[skill.category]) {
                    acc[skill.category] = [];
                }
                acc[skill.category].push(skill);
                return acc;
            }, {});

            // Render skills
            Object.entries(skillsByCategory).forEach(([category, categorySkills]) => {
                const categoryElement = document.querySelector(`[data-category="${category}"] .skills-list`);
                if (categoryElement) {
                    categoryElement.innerHTML = categorySkills.map(skill => `
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-name">${skill.name}</span>
                                <span class="skill-percentage">${skill.level}%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="${skill.level}"></div>
                            </div>
                        </div>
                    `).join('');
                }
            });
        }
    }

    renderExperience(experience) {
        const timelineContainer = document.querySelector('.experience-timeline');
        if (timelineContainer && experience.length > 0) {
            timelineContainer.innerHTML = experience.map((exp, index) => `
                <div class="timeline-item ${index % 2 === 0 ? 'left' : 'right'}">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <h3 class="timeline-title">${exp.title}</h3>
                            <div class="timeline-company">${exp.company}</div>
                            <div class="timeline-date">
                                ${this.formatDate(exp.startDate)} - ${exp.current ? 'Present' : this.formatDate(exp.endDate)}
                            </div>
                        </div>
                        <p class="timeline-description">${exp.description}</p>
                        ${exp.achievements ? `
                            <ul class="timeline-achievements">
                                ${exp.achievements.map(achievement => `<li>${achievement}</li>`).join('')}
                            </ul>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }
    }

    renderBlogPosts(posts) {
        const blogContainer = document.querySelector('.blog-posts');
        if (blogContainer && posts.length > 0) {
            blogContainer.innerHTML = posts.slice(0, 3).map(post => `
                <article class="blog-post">
                    <div class="post-meta">
                        <span class="post-date">${this.formatDate(post.date)}</span>
                        <span class="post-read-time">${post.readTime} min read</span>
                    </div>
                    <h3 class="post-title">${post.title}</h3>
                    <p class="post-excerpt">${post.excerpt}</p>
                    <div class="post-tags">
                        ${post.tags.map(tag => `<span class="post-tag">${tag}</span>`).join('')}
                    </div>
                </article>
            `).join('');
        }
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short'
        });
    }

    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.error('Service Worker registration failed:', error);
                });
        }
    }

    // Event Handlers
    handleScroll() {
        this.updateActiveNavigation();
        this.updateScrollProgress();
        this.handleParallaxEffects();
    }

    handleResize() {
        this.updateLayout();
        this.recalculateAnimations();
    }

    updateLayout() {
        // Update layout calculations on resize
        const navbar = document.getElementById('navbar');
        if (navbar) {
            document.documentElement.style.setProperty('--navbar-height', navbar.offsetHeight + 'px');
        }
    }

    recalculateAnimations() {
        // Recalculate animation positions
        const parallaxElements = document.querySelectorAll('.parallax');
        parallaxElements.forEach(element => {
            element.style.transform = 'translateY(0)';
        });
    }

    pauseAnimations() {
        document.body.classList.add('animations-paused');
    }

    resumeAnimations() {
        document.body.classList.remove('animations-paused');
    }

    handleParallaxEffects() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.parallax-bg');
        
        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    }

    handleVisibilityChange() {
        if (document.hidden) {
            this.pauseAnimations();
        } else {
            this.resumeAnimations();
        }
    }

    async handleContactForm(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        try {
            // Validate form
            if (!this.validateContactForm(data)) {
                return;
            }
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            this.setButtonLoading(submitBtn, true);
            
            // Save to database
            if (window.portfolioDb) {
                await window.portfolioDb.saveContactMessage(data);
            }
            
            // Show success message
            this.showNotification('Message sent successfully! I\'ll get back to you soon.', 'success');
            
            // Reset form
            form.reset();
            
            // Track form submission
            this.trackEvent('contact_form_submit', data);
            
        } catch (error) {
            console.error('Error submitting contact form:', error);
            this.showNotification('Failed to send message. Please try again.', 'error');
        } finally {
            // Reset button state
            const submitBtn = form.querySelector('button[type="submit"]');
            this.setButtonLoading(submitBtn, false);
        }
    }

    handleAnimationIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
                this.animationObserver.unobserve(entry.target);
            }
        });
    }

    handleError(error) {
        console.error('Application error:', error);
        
        // Track error
        if (window.portfolioDb) {
            window.portfolioDb.trackEvent('error', {
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });
        }
        
        // Show user-friendly error message
        this.showNotification('Something went wrong. Please refresh the page.', 'error');
    }

    // Utility Methods
    scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            const navbarHeight = document.getElementById('navbar').offsetHeight;
            const targetPosition = section.offsetTop - navbarHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
            
            this.currentSection = sectionId;
            this.trackEvent('section_view', { section: sectionId });
        }
    }

    updateActiveNavigation() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');
        const scrollPos = window.scrollY + 100;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${sectionId}`) {
                        link.classList.add('active');
                    }
                });
                
                if (this.currentSection !== sectionId) {
                    this.currentSection = sectionId;
                    this.trackEvent('section_view', { section: sectionId });
                }
            }
        });
    }

    updateScrollProgress() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        // Update scroll progress indicator if it exists
        const progressBar = document.querySelector('.scroll-progress');
        if (progressBar) {
            progressBar.style.width = `${scrollPercent}%`;
        }
    }

    validateContactForm(data) {
        const errors = [];
        
        if (!data.name || data.name.trim().length < 2) {
            errors.push('Name must be at least 2 characters long');
        }
        
        if (!data.email || !this.isValidEmail(data.email)) {
            errors.push('Please enter a valid email address');
        }
        
        if (!data.subject || data.subject.trim().length < 5) {
            errors.push('Subject must be at least 5 characters long');
        }
        
        if (!data.message || data.message.trim().length < 10) {
            errors.push('Message must be at least 10 characters long');
        }
        
        if (errors.length > 0) {
            this.showNotification(errors.join('\n'), 'error');
            return false;
        }
        
        return true;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="notification-icon fas ${this.getNotificationIcon(type)}"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-close" aria-label="Close notification">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Auto-remove notification
        setTimeout(() => {
            this.closeNotification(notification);
        }, 5000);
        
        // Close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.closeNotification(notification);
        });
    }

    closeNotification(notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            const text = button.querySelector('.btn-text');
            const loader = button.querySelector('.btn-loading');
            if (text) text.style.opacity = '0';
            if (loader) loader.style.opacity = '1';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            const text = button.querySelector('.btn-text');
            const loader = button.querySelector('.btn-loading');
            if (text) text.style.opacity = '1';
            if (loader) loader.style.opacity = '0';
        }
    }

    closeModal(modal) {
        if (modal) {
            modal.classList.add('hide');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    trackEvent(eventName, data = {}) {
        if (window.portfolioDb) {
            window.portfolioDb.trackEvent(eventName, data);
        }
        
        // Also track with Google Analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, data);
        }
    }

    trackPageView() {
        this.trackEvent('page_view', {
            page: window.location.pathname,
            referrer: document.referrer,
            timestamp: new Date().toISOString()
        });
    }

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }
}

// Initialize the application
const portfolioApp = new PortfolioApp();

// Export for global access
window.portfolioApp = portfolioApp;

// Additional initialization for specific features
document.addEventListener('DOMContentLoaded', () => {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = this.getAttribute('data-tooltip');
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
            
            setTimeout(() => tooltip.classList.add('show'), 10);
            
            this.addEventListener('mouseleave', function() {
                tooltip.remove();
            }, { once: true });
        });
    });
    
    // Initialize copy to clipboard functionality
    const copyButtons = document.querySelectorAll('[data-copy]');
    copyButtons.forEach(button => {
        button.addEventListener('click', async function() {
            const textToCopy = this.getAttribute('data-copy');
            try {
                await navigator.clipboard.writeText(textToCopy);
                portfolioApp.showNotification('Copied to clipboard!', 'success');
            } catch (err) {
                console.error('Failed to copy:', err);
                portfolioApp.showNotification('Failed to copy', 'error');
            }
        });
    });
    
    // Initialize print functionality
    const printButton = document.getElementById('printBtn');
    if (printButton) {
        printButton.addEventListener('click', () => {
            window.print();
            portfolioApp.trackEvent('portfolio_print');
        });
    }
    
    // Initialize share functionality
    const shareButton = document.getElementById('shareBtn');
    if (shareButton) {
        shareButton.addEventListener('click', async () => {
            if (navigator.share) {
                try {
                    await navigator.share({
                        title: 'Mohammed Mohammed Al-Ashrafi - Portfolio',
                        text: 'Check out my portfolio website',
                        url: window.location.href
                    });
                    portfolioApp.trackEvent('portfolio_share', { method: 'native' });
                } catch (err) {
                    console.error('Error sharing:', err);
                }
            } else {
                // Fallback: copy URL to clipboard
                try {
                    await navigator.clipboard.writeText(window.location.href);
                    portfolioApp.showNotification('URL copied to clipboard!', 'success');
                    portfolioApp.trackEvent('portfolio_share', { method: 'clipboard' });
                } catch (err) {
                    console.error('Failed to copy URL:', err);
                }
            }
        });
    }
});

// Register service worker
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}