/* ===== ANIMATIONS & EFFECTS ===== */

/* ===== SCROLL ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.fade-in.animate {
  opacity: 1;
  transform: translateY(0);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s ease-out;
}

.fade-in-left.animate {
  opacity: 1;
  transform: translateX(0);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.6s ease-out;
}

.fade-in-right.animate {
  opacity: 1;
  transform: translateX(0);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.6s ease-out;
}

.scale-in.animate {
  opacity: 1;
  transform: scale(1);
}

.slide-in {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease-out;
}

.slide-in.animate {
  opacity: 1;
  transform: translateY(0);
}

/* ===== STAGGER ANIMATIONS ===== */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease-out;
}

.stagger-item.animate {
  opacity: 1;
  transform: translateY(0);
}

.stagger-item:nth-child(1) { transition-delay: 0.1s; }
.stagger-item:nth-child(2) { transition-delay: 0.2s; }
.stagger-item:nth-child(3) { transition-delay: 0.3s; }
.stagger-item:nth-child(4) { transition-delay: 0.4s; }
.stagger-item:nth-child(5) { transition-delay: 0.5s; }
.stagger-item:nth-child(6) { transition-delay: 0.6s; }

/* ===== HOVER ANIMATIONS ===== */
.hover-lift {
  transition: all var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.hover-glow {
  position: relative;
  overflow: hidden;
}

.hover-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: inherit;
  opacity: 0;
  z-index: -1;
  transition: opacity var(--transition-normal);
  filter: blur(15px);
}

.hover-glow:hover::before {
  opacity: 0.3;
}

.hover-scale {
  transition: transform var(--transition-fast);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform var(--transition-normal);
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* ===== TEXT ANIMATIONS ===== */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--primary-color);
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--primary-color); }
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.text-reveal {
  position: relative;
  overflow: hidden;
}

.text-reveal span {
  display: inline-block;
  transform: translateY(100%);
  transition: transform 0.6s ease;
}

.text-reveal.animate span {
  transform: translateY(0);
}

/* ===== LOADING ANIMATIONS ===== */
.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

[data-theme="dark"] .skeleton {
  background: linear-gradient(90deg, var(--gray-700) 25%, var(--gray-600) 50%, var(--gray-700) 75%);
  background-size: 200% 100%;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

[data-theme="dark"] .spinner {
  border-color: var(--gray-700);
  border-top-color: var(--primary-color);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dots-loading {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
}

.dots-loading div {
  position: absolute;
  top: 33px;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: var(--primary-color);
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.dots-loading div:nth-child(1) {
  left: 8px;
  animation: dots1 0.6s infinite;
}

.dots-loading div:nth-child(2) {
  left: 8px;
  animation: dots2 0.6s infinite;
}

.dots-loading div:nth-child(3) {
  left: 32px;
  animation: dots2 0.6s infinite;
}

.dots-loading div:nth-child(4) {
  left: 56px;
  animation: dots3 0.6s infinite;
}

@keyframes dots1 {
  0% { transform: scale(0); }
  100% { transform: scale(1); }
}

@keyframes dots3 {
  0% { transform: scale(1); }
  100% { transform: scale(0); }
}

@keyframes dots2 {
  0% { transform: translate(0, 0); }
  100% { transform: translate(24px, 0); }
}

/* ===== PARTICLE ANIMATIONS ===== */
.particles-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: -1;
}

.particle {
  position: absolute;
  background: var(--primary-color);
  border-radius: 50%;
  pointer-events: none;
  opacity: 0.1;
  animation: float-particle 10s linear infinite;
}

.particle:nth-child(odd) {
  background: var(--secondary-color);
  animation-duration: 15s;
}

@keyframes float-particle {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(-10vh) rotate(360deg);
    opacity: 0;
  }
}

/* ===== WAVE ANIMATIONS ===== */
.wave {
  position: relative;
  overflow: hidden;
}

.wave::before,
.wave::after {
  content: '';
  position: absolute;
  left: 50%;
  min-width: 300px;
  min-height: 300px;
  background-color: var(--white);
  border-radius: 45%;
  transform: translate(-50%, -50%) rotate(0deg);
  animation: wave-rotate 6s linear infinite;
}

.wave::before {
  opacity: 0.1;
  animation-delay: 0s;
}

.wave::after {
  opacity: 0.05;
  animation-delay: -3s;
}

@keyframes wave-rotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ===== MORPHING ANIMATIONS ===== */
.morph {
  animation: morphing 8s ease-in-out infinite;
}

@keyframes morphing {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

/* ===== GLITCH EFFECTS ===== */
.glitch {
  position: relative;
  display: inline-block;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-anim-1 0.5s infinite linear alternate-reverse;
  color: var(--primary-color);
  z-index: -1;
  clip: rect(0, 900px, 0, 0);
}

.glitch::after {
  animation: glitch-anim-2 0.5s infinite linear alternate-reverse;
  color: var(--secondary-color);
  z-index: -2;
  clip: rect(0, 900px, 0, 0);
}

@keyframes glitch-anim-1 {
  0% {
    clip: rect(42px, 9999px, 44px, 0);
    transform: skew(0.85deg);
  }
  5% {
    clip: rect(12px, 9999px, 59px, 0);
    transform: skew(0.4deg);
  }
  10% {
    clip: rect(16px, 9999px, 29px, 0);
    transform: skew(0.7deg);
  }
  15% {
    clip: rect(18px, 9999px, 83px, 0);
    transform: skew(0.1deg);
  }
  20% {
    clip: rect(54px, 9999px, 15px, 0);
    transform: skew(0.5deg);
  }
  25% {
    clip: rect(63px, 9999px, 81px, 0);
    transform: skew(0.3deg);
  }
  30% {
    clip: rect(17px, 9999px, 39px, 0);
    transform: skew(0.9deg);
  }
  35% {
    clip: rect(28px, 9999px, 92px, 0);
    transform: skew(0.2deg);
  }
  40% {
    clip: rect(34px, 9999px, 43px, 0);
    transform: skew(0.6deg);
  }
  45% {
    clip: rect(19px, 9999px, 76px, 0);
    transform: skew(0.8deg);
  }
  50% {
    clip: rect(23px, 9999px, 87px, 0);
    transform: skew(0.4deg);
  }
  55% {
    clip: rect(67px, 9999px, 32px, 0);
    transform: skew(0.7deg);
  }
  60% {
    clip: rect(45px, 9999px, 98px, 0);
    transform: skew(0.1deg);
  }
  65% {
    clip: rect(56px, 9999px, 21px, 0);
    transform: skew(0.5deg);
  }
  70% {
    clip: rect(89px, 9999px, 65px, 0);
    transform: skew(0.3deg);
  }
  75% {
    clip: rect(31px, 9999px, 74px, 0);
    transform: skew(0.9deg);
  }
  80% {
    clip: rect(78px, 9999px, 48px, 0);
    transform: skew(0.2deg);
  }
  85% {
    clip: rect(52px, 9999px, 86px, 0);
    transform: skew(0.6deg);
  }
  90% {
    clip: rect(61px, 9999px, 37px, 0);
    transform: skew(0.8deg);
  }
  95% {
    clip: rect(25px, 9999px, 93px, 0);
    transform: skew(0.4deg);
  }
  100% {
    clip: rect(14px, 9999px, 51px, 0);
    transform: skew(0.7deg);
  }
}

@keyframes glitch-anim-2 {
  0% {
    clip: rect(65px, 9999px, 100px, 0);
    transform: skew(0.2deg);
  }
  5% {
    clip: rect(52px, 9999px, 74px, 0);
    transform: skew(0.6deg);
  }
  10% {
    clip: rect(79px, 9999px, 85px, 0);
    transform: skew(0.4deg);
  }
  15% {
    clip: rect(46px, 9999px, 91px, 0);
    transform: skew(0.8deg);
  }
  20% {
    clip: rect(33px, 9999px, 57px, 0);
    transform: skew(0.1deg);
  }
  25% {
    clip: rect(88px, 9999px, 42px, 0);
    transform: skew(0.7deg);
  }
  30% {
    clip: rect(26px, 9999px, 68px, 0);
    transform: skew(0.3deg);
  }
  35% {
    clip: rect(71px, 9999px, 29px, 0);
    transform: skew(0.9deg);
  }
  40% {
    clip: rect(15px, 9999px, 84px, 0);
    transform: skew(0.5deg);
  }
  45% {
    clip: rect(58px, 9999px, 36px, 0);
    transform: skew(0.2deg);
  }
  50% {
    clip: rect(92px, 9999px, 63px, 0);
    transform: skew(0.6deg);
  }
  55% {
    clip: rect(41px, 9999px, 77px, 0);
    transform: skew(0.4deg);
  }
  60% {
    clip: rect(83px, 9999px, 19px, 0);
    transform: skew(0.8deg);
  }
  65% {
    clip: rect(27px, 9999px, 95px, 0);
    transform: skew(0.1deg);
  }
  70% {
    clip: rect(69px, 9999px, 44px, 0);
    transform: skew(0.7deg);
  }
  75% {
    clip: rect(16px, 9999px, 72px, 0);
    transform: skew(0.3deg);
  }
  80% {
    clip: rect(54px, 9999px, 87px, 0);
    transform: skew(0.9deg);
  }
  85% {
    clip: rect(38px, 9999px, 31px, 0);
    transform: skew(0.5deg);
  }
  90% {
    clip: rect(75px, 9999px, 66px, 0);
    transform: skew(0.2deg);
  }
  95% {
    clip: rect(22px, 9999px, 89px, 0);
    transform: skew(0.6deg);
  }
  100% {
    clip: rect(47px, 9999px, 53px, 0);
    transform: skew(0.4deg);
  }
}

/* ===== NEON GLOW EFFECTS ===== */
.neon-glow {
  color: var(--primary-color);
  text-shadow: 
    0 0 5px var(--primary-color),
    0 0 10px var(--primary-color),
    0 0 15px var(--primary-color),
    0 0 20px var(--primary-color),
    0 0 35px var(--primary-color),
    0 0 40px var(--primary-color);
  animation: neon-flicker 1.5s infinite alternate;
}

@keyframes neon-flicker {
  0%, 18%, 22%, 25%, 53%, 57%, 100% {
    text-shadow: 
      0 0 5px var(--primary-color),
      0 0 10px var(--primary-color),
      0 0 15px var(--primary-color),
      0 0 20px var(--primary-color),
      0 0 35px var(--primary-color),
      0 0 40px var(--primary-color);
  }
  20%, 24%, 55% {
    text-shadow: none;
  }
}

/* ===== FLOATING ANIMATIONS ===== */
.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-slow {
  animation: float-slow 4s ease-in-out infinite;
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

/* ===== BOUNCE ANIMATIONS ===== */
.bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.bounce-slow {
  animation: bounce-slow 3s infinite;
}

@keyframes bounce-slow {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* ===== SHAKE ANIMATIONS ===== */
.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* ===== HEARTBEAT ANIMATION ===== */
.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  14% { transform: scale(1.1); }
  28% { transform: scale(1); }
  42% { transform: scale(1.1); }
  70% { transform: scale(1); }
}

/* ===== ZOOM ANIMATIONS ===== */
.zoom-in {
  animation: zoom-in 0.5s ease-out;
}

@keyframes zoom-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.zoom-out {
  animation: zoom-out 0.5s ease-out;
}

@keyframes zoom-out {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}

/* ===== SLIDE ANIMATIONS ===== */
.slide-in-left {
  animation: slide-in-left 0.5s ease-out;
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-up {
  animation: slide-in-up 0.5s ease-out;
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-down {
  animation: slide-in-down 0.5s ease-out;
}

@keyframes slide-in-down {
  0% {
    opacity: 0;
    transform: translateY(-100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== FLIP ANIMATIONS ===== */
.flip-horizontal {
  animation: flip-horizontal 0.6s ease-in-out;
}

@keyframes flip-horizontal {
  0% { transform: rotateX(0); }
  50% { transform: rotateX(-90deg); }
  100% { transform: rotateX(0); }
}

.flip-vertical {
  animation: flip-vertical 0.6s ease-in-out;
}

@keyframes flip-vertical {
  0% { transform: rotateY(0); }
  50% { transform: rotateY(-90deg); }
  100% { transform: rotateY(0); }
}

/* ===== PARALLAX EFFECTS ===== */
.parallax {
  transform: translateZ(0);
  transition: transform 0.1s ease-out;
}

.parallax-bg {
  will-change: transform;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== CUSTOM CURSOR STYLES ===== */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  pointer-events: none;
  z-index: var(--z-tooltip);
  mix-blend-mode: difference;
  transition: all 0.1s ease;
}

.cursor-follower {
  position: fixed;
  width: 40px;
  height: 40px;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  pointer-events: none;
  z-index: var(--z-tooltip);
  transition: all 0.1s ease;
}

.custom-cursor.hover,
.cursor-follower.hover {
  transform: scale(1.5);
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* ===== MAGNETIC EFFECT ===== */
.magnetic {
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* ===== IMAGE HOVER EFFECTS ===== */
.hover-image {
  transition: all var(--transition-normal);
  overflow: hidden;
}

.hover-image img {
  transition: all var(--transition-slow);
}

.hover-image:hover img {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.1);
}

/* ===== BUTTON RIPPLE EFFECT ===== */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn-ripple:active::after {
  width: 300px;
  height: 300px;
}

/* ===== LOADING STATES ===== */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* ===== ACCORDION ANIMATIONS ===== */
.accordion-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.accordion-content.open {
  max-height: 500px;
}

/* ===== PROGRESS BAR ANIMATIONS ===== */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    rgba(255, 255, 255, 0) 100%);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .typewriter {
    animation: none;
    border-right: none;
  }
  
  .particles-bg,
  .particle {
    display: none;
  }
  
  .wave::before,
  .wave::after {
    animation: none;
  }
  
  .morph {
    animation: none;
  }
  
  .glitch::before,
  .glitch::after {
    animation: none;
  }
}