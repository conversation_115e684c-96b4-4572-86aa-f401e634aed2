/* ===== COMPONENT STYLES ===== */

/* ===== SKILLS SECTION ===== */
.skills {
  background: var(--bg-primary);
}

.skills-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
}

.skill-category {
  background: var(--bg-card);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.skill-category:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.category-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-6);
  color: var(--text-primary);
  text-align: center;
  position: relative;
}

.category-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.skill-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.skill-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skill-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.skill-percentage {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
}

.skill-bar {
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

[data-theme="dark"] .skill-bar {
  background: var(--gray-700);
}

.skill-progress {
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  width: 0;
  transition: width 1s ease-in-out;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
  background: var(--bg-secondary);
}

.portfolio-filters {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--space-3) var(--space-6);
  background: var(--bg-card);
  border: 2px solid var(--gray-200);
  color: var(--text-secondary);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.filter-btn:hover,
.filter-btn.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-color: transparent;
  color: var(--white);
  transform: translateY(-2px);
}

[data-theme="dark"] .filter-btn {
  background: var(--gray-800);
  border-color: var(--gray-600);
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.portfolio-item {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  opacity: 0;
  transform: translateY(20px);
  cursor: pointer;
}

.portfolio-item.show {
  opacity: 1;
  transform: translateY(0);
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-2xl);
}

.portfolio-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.9), 
    rgba(240, 147, 251, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-links {
  display: flex;
  gap: var(--space-4);
}

.portfolio-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: var(--white);
  color: var(--primary-color);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.portfolio-link:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.portfolio-content {
  padding: var(--space-6);
}

.portfolio-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-2);
  color: var(--text-primary);
}

.portfolio-description {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.6;
}

.portfolio-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.portfolio-tag {
  padding: var(--space-1) var(--space-3);
  background: var(--gray-100);
  color: var(--text-secondary);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

[data-theme="dark"] .portfolio-tag {
  background: var(--gray-700);
  color: var(--gray-300);
}

.portfolio-featured {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

.portfolio-load-more {
  text-align: center;
}

/* ===== PROJECT MODAL ===== */
.project-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.project-modal.show {
  opacity: 1;
  visibility: visible;
}

.project-modal.hide {
  opacity: 0;
  visibility: hidden;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-overlay);
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  background: var(--bg-card);
  border-radius: var(--radius-2xl);
  max-width: 800px;
  max-height: 90vh;
  margin: 5vh auto;
  overflow: auto;
  box-shadow: var(--shadow-2xl);
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.project-modal.show .modal-content {
  transform: scale(1);
}

.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  width: 40px;
  height: 40px;
  background: var(--bg-card);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--gray-100);
  transform: scale(1.1);
}

[data-theme="dark"] .modal-close:hover {
  background: var(--gray-700);
}

.modal-header {
  position: relative;
}

.modal-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.modal-title-section {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--space-8) var(--space-6) var(--space-6);
  color: var(--white);
}

.modal-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
}

.modal-meta {
  display: flex;
  gap: var(--space-4);
  font-size: var(--text-sm);
  opacity: 0.9;
}

.modal-body {
  padding: var(--space-6);
}

.modal-description {
  font-size: var(--text-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-6);
  color: var(--text-secondary);
}

.modal-section {
  margin-bottom: var(--space-6);
}

.modal-section h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.modal-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.modal-tag {
  padding: var(--space-2) var(--space-3);
  background: var(--gray-100);
  color: var(--text-secondary);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
}

[data-theme="dark"] .modal-tag {
  background: var(--gray-700);
  color: var(--gray-300);
}

.modal-features {
  list-style: none;
  padding: 0;
}

.modal-features li {
  position: relative;
  padding: var(--space-2) 0 var(--space-2) var(--space-6);
  color: var(--text-secondary);
}

.modal-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: var(--font-weight-bold);
}

.modal-actions {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
  padding-top: var(--space-6);
  border-top: 1px solid var(--gray-200);
}

[data-theme="dark"] .modal-actions {
  border-top-color: var(--gray-700);
}

/* ===== EXPERIENCE SECTION ===== */
.experience {
  background: var(--bg-primary);
}

.experience-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.experience-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-12);
  display: flex;
  align-items: center;
}

.timeline-item:nth-child(odd) {
  flex-direction: row-reverse;
}

.timeline-item:nth-child(odd) .timeline-content {
  text-align: right;
  padding-right: var(--space-8);
}

.timeline-item:nth-child(even) .timeline-content {
  text-align: left;
  padding-left: var(--space-8);
}

.timeline-marker {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  border: 4px solid var(--bg-primary);
  z-index: 1;
}

.timeline-content {
  background: var(--bg-card);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  width: calc(50% - 40px);
  transition: all var(--transition-normal);
}

.timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.timeline-date {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-3);
}

.timeline-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.timeline-company {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  margin-bottom: var(--space-3);
}

.timeline-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* ===== SERVICES SECTION ===== */
.services {
  background: var(--bg-secondary);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
}

.service-card {
  background: var(--bg-card);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(102, 126, 234, 0.1), 
    transparent);
  transition: left var(--transition-slow);
}

.service-card:hover::before {
  left: 100%;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-2xl);
}

.service-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-6);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-2xl);
  color: var(--white);
  font-size: var(--text-2xl);
}

.service-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.service-description {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.service-features {
  list-style: none;
  text-align: left;
}

.service-features li {
  padding: var(--space-2) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--space-6);
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

/* ===== CONTACT SECTION ===== */
.contact {
  background: var(--bg-primary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: start;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.contact-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  border-radius: var(--radius-xl);
  font-size: var(--text-xl);
  flex-shrink: 0;
}

.contact-details h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.contact-details p {
  color: var(--text-secondary);
  margin: 0;
}

.contact-social {
  margin-top: var(--space-4);
}

.contact-social h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.social-links {
  display: flex;
  gap: var(--space-4);
}

.social-link {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-100);
  color: var(--text-secondary);
  border-radius: var(--radius-xl);
  transition: all var(--transition-fast);
  font-size: var(--text-lg);
}

.social-link:hover {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  transform: translateY(-3px);
}

[data-theme="dark"] .social-link {
  background: var(--gray-700);
  color: var(--gray-300);
}

/* ===== CONTACT FORM ===== */
.contact-form {
  background: var(--bg-card);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
}

.form-group {
  margin-bottom: var(--space-6);
}

.form-group label {
  display: block;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-family: var(--font-primary);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error,
.form-group textarea.error {
  border-color: var(--error-color);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea {
  background: var(--gray-800);
  border-color: var(--gray-600);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.field-error {
  color: var(--error-color);
  font-size: var(--text-sm);
  margin-top: var(--space-1);
}

/* ===== BLOG SECTION ===== */
.blog-post {
  background: var(--bg-card);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  margin-bottom: var(--space-6);
}

.blog-post:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.post-meta {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-3);
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.post-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-3);
  color: var(--text-primary);
}

.post-excerpt {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  line-height: var(--line-height-relaxed);
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.post-tag {
  padding: var(--space-1) var(--space-2);
  background: var(--gray-100);
  color: var(--text-secondary);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

[data-theme="dark"] .post-tag {
  background: var(--gray-700);
  color: var(--gray-300);
}

.post-read-more {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.post-read-more:hover {
  color: var(--secondary-color);
}

/* ===== POST MODAL ===== */
.post-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.post-modal.show {
  opacity: 1;
  visibility: visible;
}

.post-modal .modal-content {
  max-width: 900px;
  padding: 0;
}

.post-content {
  padding: var(--space-8);
}

.post-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

[data-theme="dark"] .post-header {
  border-bottom-color: var(--gray-700);
}

.post-header .post-title {
  font-size: var(--text-3xl);
  margin-bottom: var(--space-4);
}

.post-header .post-meta {
  margin-bottom: var(--space-4);
}

.post-body {
  font-size: var(--text-lg);
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
}

.post-body p {
  margin-bottom: var(--space-6);
}

.post-body h2 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.post-body h3 {
  margin-top: var(--space-6);
  margin-bottom: var(--space-3);
  color: var(--text-primary);
}

.post-body code {
  background: var(--gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: 0.9em;
}

[data-theme="dark"] .post-body code {
  background: var(--gray-700);
}

.post-body strong {
  color: var(--text-primary);
}

.post-body em {
  color: var(--text-primary);
}

/* ===== FOOTER ===== */
.footer {
  background: var(--gray-900);
  color: var(--white);
  padding: var(--space-8) 0;
  text-align: center;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-text p {
  margin: 0;
  color: var(--gray-300);
}

.footer-text p:first-child {
  font-weight: var(--font-weight-medium);
}

.footer-links {
  display: flex;
  gap: var(--space-6);
}

.footer-link {
  color: var(--gray-300);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--white);
}

/* ===== TOOLTIPS ===== */
.tooltip {
  position: absolute;
  background: var(--gray-900);
  color: var(--white);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  z-index: var(--z-tooltip);
  opacity: 0;
  transform: translateY(-5px);
  transition: all var(--transition-fast);
  pointer-events: none;
  white-space: nowrap;
}

.tooltip.show {
  opacity: 1;
  transform: translateY(0);
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--gray-900);
}

/* ===== CV SECTION ===== */
.cv {
    background: var(--bg-color);
    padding: 4rem 0;
}

.cv-content {
    max-width: 900px;
    margin: 0 auto;
    background: var(--card-bg);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.cv-header {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    padding: 3rem;
    background: var(--gradient-bg);
    color: white;
}

.cv-personal h1.cv-name {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #fff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cv-title {
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cv-contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.cv-contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
}

.cv-contact-item i {
    width: 20px;
    text-align: center;
    opacity: 0.8;
}

.cv-avatar {
    display: flex;
    justify-content: center;
    align-items: center;
}

.cv-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.cv-section {
    padding: 2.5rem 3rem;
    border-bottom: 1px solid var(--border-color);
}

.cv-section:last-child {
    border-bottom: none;
}

.cv-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.cv-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.cv-section-content {
    line-height: 1.8;
}

.cv-experience-item,
.cv-education-item {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: var(--bg-light);
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
}

.cv-experience-item:last-child,
.cv-education-item:last-child {
    margin-bottom: 0;
}

.cv-experience-header,
.cv-education-header {
    margin-bottom: 1rem;
}

.cv-job-title,
.cv-degree {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.cv-company,
.cv-school {
    font-size: 1.1rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.cv-date {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
}

.cv-job-description p,
.cv-education-details p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.cv-achievements {
    list-style: none;
    padding-left: 0;
    margin-top: 1rem;
}

.cv-achievements li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.cv-achievements li::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.cv-skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.cv-skill-category h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.cv-skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.cv-skill-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    white-space: nowrap;
}

.cv-certifications {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.cv-cert-item {
    padding: 1.5rem;
    background: var(--bg-light);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.cv-cert-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.cv-cert-item p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
}

.cv-languages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.cv-language-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--bg-light);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.cv-language-name {
    font-weight: 600;
    color: var(--text-primary);
}

.cv-language-level {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.9rem;
}

.cv-actions {
    padding: 2rem 3rem;
    background: var(--bg-light);
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* CV Responsive Design */
@media (max-width: 768px) {
    .cv-header {
        grid-template-columns: 1fr;
        text-align: center;
        padding: 2rem;
    }
    
    .cv-personal h1.cv-name {
        font-size: 2rem;
    }
    
    .cv-contact-info {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .cv-photo {
        width: 120px;
        height: 120px;
    }
    
    .cv-section {
        padding: 2rem;
    }
    
    .cv-skills-grid {
        grid-template-columns: 1fr;
    }
    
    .cv-certifications {
        grid-template-columns: 1fr;
    }
    
    .cv-languages {
        grid-template-columns: 1fr;
    }
    
    .cv-actions {
        padding: 1.5rem;
        flex-direction: column;
    }
}

/* Print Styles for CV */
@media print {
    .cv {
        padding: 0;
    }
    
    .cv-content {
        box-shadow: none;
        border-radius: 0;
    }
    
    .cv-header {
        background: #333 !important;
        color: white !important;
    }
    
    .cv-actions {
        display: none;
    }
    
    .cv-section {
        page-break-inside: avoid;
    }
}