/* ===== RESPONSIVE DESIGN ===== */

/* ===== BREAKPOINTS ===== */
/* 
  xs: 0px
  sm: 576px
  md: 768px
  lg: 1024px
  xl: 1200px
  2xl: 1400px
*/

/* ===== EXTRA LARGE SCREENS (1400px and up) ===== */
@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }
  
  .hero-content {
    gap: var(--space-20);
  }
  
  .hero-avatar {
    width: 350px;
    height: 350px;
  }
  
  .about-img-container {
    width: 450px;
    height: 550px;
  }
  
  .section {
    padding: var(--space-24) 0;
  }
  
  .section-title {
    font-size: var(--text-5xl);
  }
  
  .name {
    font-size: var(--text-6xl);
  }
}

/* ===== LARGE SCREENS (1200px - 1399px) ===== */
@media (min-width: 1200px) and (max-width: 1399px) {
  .container {
    max-width: 1200px;
  }
  
  .hero-avatar {
    width: 320px;
    height: 320px;
  }
  
  .about-img-container {
    width: 380px;
    height: 480px;
  }
}

/* ===== MEDIUM-LARGE SCREENS (1024px - 1199px) ===== */
@media (min-width: 1024px) and (max-width: 1199px) {
  .container {
    max-width: 1024px;
    padding: 0 var(--space-8);
  }
  
  .hero-content {
    gap: var(--space-12);
  }
  
  .hero-avatar {
    width: 280px;
    height: 280px;
  }
  
  .about-content {
    gap: var(--space-12);
  }
  
  .about-img-container {
    width: 350px;
    height: 450px;
  }
  
  .skills-categories {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .portfolio-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .timeline-content {
    width: calc(50% - 30px);
  }
}

/* ===== MEDIUM SCREENS (768px - 1023px) ===== */
@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    max-width: 768px;
    padding: 0 var(--space-6);
  }
  
  .section {
    padding: var(--space-16) 0;
  }
  
  .section-title {
    font-size: var(--text-3xl);
  }
  
  .section-subtitle {
    font-size: var(--text-base);
  }
  
  /* Navigation */
  .hamburger {
    display: flex;
  }
  
  .nav-menu {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--bg-card);
    flex-direction: column;
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: var(--z-dropdown);
  }
  
  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav-item {
    width: 100%;
  }
  
  .nav-link {
    display: block;
    text-align: center;
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
  }
  
  .nav-link::after {
    display: none;
  }
  
  /* Hero Section */
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }
  
  .hero-avatar {
    width: 250px;
    height: 250px;
    order: -1;
  }
  
  .name {
    font-size: var(--text-4xl);
  }
  
  .title-accent {
    font-size: var(--text-xl);
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  /* About Section */
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }
  
  .about-img-container {
    width: 300px;
    height: 400px;
    margin: 0 auto;
  }
  
  .about-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
  
  /* Skills Section */
  .skills-categories {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  /* Portfolio Section */
  .portfolio-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
  
  .portfolio-filters {
    gap: var(--space-3);
  }
  
  .filter-btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
  }
  
  /* Timeline */
  .experience-timeline::before {
    left: 30px;
  }
  
  .timeline-marker {
    left: 30px;
  }
  
  .timeline-item {
    flex-direction: row !important;
    margin-left: 60px;
  }
  
  .timeline-content {
    width: 100% !important;
    text-align: left !important;
    padding: var(--space-6) !important;
  }
  
  /* Services Section */
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
  
  /* Contact Section */
  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }
  
  .contact-info {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  
  .contact-item {
    flex-basis: calc(50% - var(--space-4));
  }
  
  .contact-social {
    flex-basis: 100%;
    text-align: center;
  }
}

/* ===== SMALL-MEDIUM SCREENS (576px - 767px) ===== */
@media (min-width: 576px) and (max-width: 767px) {
  .container {
    max-width: 576px;
    padding: 0 var(--space-4);
  }
  
  .section {
    padding: var(--space-12) 0;
  }
  
  .section-title {
    font-size: var(--text-2xl);
  }
  
  .name {
    font-size: var(--text-3xl);
  }
  
  .hero-avatar {
    width: 220px;
    height: 220px;
  }
  
  .about-img-container {
    width: 280px;
    height: 350px;
  }
  
  .about-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
  
  .portfolio-grid {
    grid-template-columns: 1fr;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .contact-info {
    flex-direction: column;
    gap: var(--space-6);
  }
  
  .contact-item {
    flex-basis: auto;
  }
  
  /* Navigation */
  .nav-menu {
    top: 60px;
    padding: var(--space-4);
  }
  
  .nav-link span {
    display: block;
  }
}

/* ===== SMALL SCREENS (0px - 575px) ===== */
@media (max-width: 575px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .section {
    padding: var(--space-10) 0;
  }
  
  .section-header {
    margin-bottom: var(--space-12);
  }
  
  .section-title {
    font-size: var(--text-xl);
  }
  
  .section-subtitle {
    font-size: var(--text-sm);
  }
  
  /* Navigation */
  .nav-container {
    padding: var(--space-3) var(--space-4);
  }
  
  .nav-logo {
    font-size: var(--text-lg);
  }
  
  .nav-menu {
    top: 60px;
    padding: var(--space-4);
  }
  
  .nav-link span {
    display: block;
  }
  
  /* Hero Section */
  .hero {
    min-height: 90vh;
  }
  
  .greeting {
    font-size: var(--text-base);
  }
  
  .name {
    font-size: var(--text-2xl);
  }
  
  .title-accent {
    font-size: var(--text-lg);
  }
  
  .hero-description {
    font-size: var(--text-base);
  }
  
  .hero-avatar {
    width: 180px;
    height: 180px;
  }
  
  .avatar-container {
    width: 180px;
    height: 180px;
  }
  
  .hero-cta {
    flex-direction: column;
    gap: var(--space-3);
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 280px;
  }
  
  /* About Section */
  .about-img-container {
    width: 250px;
    height: 300px;
  }
  
  .about-stats {
    grid-template-columns: 1fr;
    gap: var(--space-4);
    text-align: center;
  }
  
  .stat-number {
    font-size: var(--text-2xl);
  }
  
  .about-actions {
    flex-direction: column;
    align-items: center;
  }
  
  /* Skills Section */
  .skill-category {
    padding: var(--space-6);
  }
  
  .category-title {
    font-size: var(--text-lg);
  }
  
  /* Portfolio Section */
  .portfolio-filters {
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
  }
  
  .filter-btn {
    width: 200px;
    text-align: center;
  }
  
  .portfolio-grid {
    gap: var(--space-6);
  }
  
  /* Timeline */
  .experience-timeline::before {
    left: 20px;
  }
  
  .timeline-marker {
    left: 20px;
  }
  
  .timeline-item {
    margin-left: 50px;
  }
  
  .timeline-content {
    padding: var(--space-4) !important;
  }
  
  .timeline-title {
    font-size: var(--text-lg);
  }
  
  .timeline-company {
    font-size: var(--text-base);
  }
  
  /* Services Section */
  .service-card {
    padding: var(--space-6);
  }
  
  .service-icon {
    width: 60px;
    height: 60px;
    font-size: var(--text-xl);
  }
  
  .service-title {
    font-size: var(--text-lg);
  }
  
  /* Contact Section */
  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
  
  .contact-icon {
    width: 50px;
    height: 50px;
    font-size: var(--text-lg);
  }
  
  .contact-form {
    padding: var(--space-6);
  }
  
  .social-links {
    justify-content: center;
  }
  
  /* Modal Adjustments */
  .modal-content {
    margin: 2vh auto;
    max-height: 96vh;
    max-width: 95%;
  }
  
  .modal-body {
    padding: var(--space-4);
  }
  
  .modal-actions {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  /* Footer */
  .footer-content {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  /* Notifications */
  .notification {
    top: var(--space-4);
    right: var(--space-4);
    left: var(--space-4);
    max-width: none;
  }
}

/* ===== EXTRA SMALL SCREENS (320px and below) ===== */
@media (max-width: 320px) {
  .container {
    padding: 0 var(--space-2);
  }
  
  .section {
    padding: var(--space-8) 0;
  }
  
  .name {
    font-size: var(--text-xl);
  }
  
  .hero-avatar {
    width: 150px;
    height: 150px;
  }
  
  .avatar-container {
    width: 150px;
    height: 150px;
  }
  
  .about-img-container {
    width: 200px;
    height: 250px;
  }
  
  .skill-category {
    padding: var(--space-4);
  }
  
  .service-card {
    padding: var(--space-4);
  }
  
  .contact-form {
    padding: var(--space-4);
  }
  
  .filter-btn {
    width: 180px;
    padding: var(--space-2) var(--space-3);
  }
  
  .timeline-content {
    padding: var(--space-3) !important;
  }
  
  .modal-content {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    max-height: 100vh;
  }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (orientation: landscape) and (max-height: 600px) {
  .hero {
    min-height: 100vh;
    padding: var(--space-8) 0;
  }
  
  .hero-content {
    gap: var(--space-8);
  }
  
  .hero-avatar {
    width: 200px;
    height: 200px;
  }
  
  .avatar-container {
    width: 200px;
    height: 200px;
  }
  
  .hero-scroll {
    display: none;
  }
  
  .section {
    padding: var(--space-12) 0;
  }
  
  .nav-menu {
    max-height: 400px;
    overflow-y: auto;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .navbar,
  .hero-scroll,
  .loading-screen,
  .contact-form,
  .footer,
  .back-to-top,
  .hamburger,
  .theme-toggle {
    display: none !important;
  }
  
  .hero {
    min-height: auto;
    padding: var(--space-8) 0;
  }
  
  .section {
    padding: var(--space-6) 0;
    break-inside: avoid;
  }
  
  .portfolio-item,
  .service-card,
  .timeline-item {
    break-inside: avoid;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .btn {
    border: 2px solid black !important;
    background: white !important;
    color: black !important;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .about-content {
    grid-template-columns: 1fr;
  }
  
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .hero-social,
  .social-links {
    display: none;
  }
}

/* ===== HIGH DPI DISPLAYS ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hero-avatar img,
  .about-img-container img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* ===== DARK MODE MEDIA QUERY ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-card: var(--gray-800);
    --text-primary: var(--white);
    --text-secondary: var(--gray-300);
    --text-muted: var(--gray-400);
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  .hero-particles,
  .avatar-ring,
  .scroll-indicator i,
  .particle,
  .wave::before,
  .wave::after {
    animation: none !important;
  }
  
  .parallax {
    transform: none !important;
  }
  
  .typewriter {
    animation: none;
    border-right: none;
  }
  
  .morph {
    animation: none;
  }
  
  .glitch::before,
  .glitch::after {
    animation: none;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0066cc;
    --secondary-color: #cc0066;
    --text-primary: #000000;
    --text-secondary: #333333;
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
  }
  
  .btn-primary {
    background: var(--primary-color) !important;
    border: 2px solid var(--primary-color) !important;
  }
  
  .btn-outline {
    border-width: 3px !important;
  }
  
  .nav-link::after {
    height: 3px !important;
  }
}

/* ===== UTILITY CLASSES FOR RESPONSIVE DESIGN ===== */
.hidden-xs { display: none !important; }
.hidden-sm { display: none !important; }
.hidden-md { display: none !important; }
.hidden-lg { display: none !important; }
.hidden-xl { display: none !important; }

@media (min-width: 576px) {
  .hidden-xs { display: initial !important; }
  .visible-sm { display: initial !important; }
}

@media (min-width: 768px) {
  .hidden-sm { display: initial !important; }
  .visible-md { display: initial !important; }
}

@media (min-width: 1024px) {
  .hidden-md { display: initial !important; }
  .visible-lg { display: initial !important; }
}

@media (min-width: 1200px) {
  .hidden-lg { display: initial !important; }
  .visible-xl { display: initial !important; }
}

@media (min-width: 1400px) {
  .hidden-xl { display: initial !important; }
}

/* ===== RESPONSIVE TEXT SIZES ===== */
@media (max-width: 767px) {
  .responsive-text-sm {
    font-size: var(--text-sm) !important;
  }
  
  .responsive-text-base {
    font-size: var(--text-base) !important;
  }
  
  .responsive-text-lg {
    font-size: var(--text-lg) !important;
  }
}

/* ===== RESPONSIVE SPACING ===== */
@media (max-width: 767px) {
  .responsive-p-sm {
    padding: var(--space-4) !important;
  }
  
  .responsive-m-sm {
    margin: var(--space-4) !important;
  }
  
  .responsive-gap-sm {
    gap: var(--space-4) !important;
  }
}

/* ===== RESPONSIVE GRID ===== */
@media (max-width: 767px) {
  .responsive-grid-1 {
    grid-template-columns: 1fr !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* ===== CONTAINER QUERIES (Future Enhancement) ===== */
/*
@container (max-width: 400px) {
  .portfolio-item {
    flex-direction: column;
  }
}
*/

/* ===== TOUCH DEVICE OPTIMIZATIONS ===== */
@media (hover: none) and (pointer: coarse) {
  .hover-lift:hover,
  .hover-scale:hover,
  .hover-rotate:hover {
    transform: none;
  }
  
  .btn:hover {
    transform: none;
  }
  
  .portfolio-item:hover {
    transform: none;
  }
  
  .service-card:hover {
    transform: none;
  }
}