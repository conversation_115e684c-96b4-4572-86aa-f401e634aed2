// ===== PORTFOLIO MANAGEMENT =====

class PortfolioManager {
    constructor() {
        this.projects = [];
        this.currentFilter = 'all';
        this.currentPage = 1;
        this.projectsPerPage = 6;
        this.portfolioGrid = document.getElementById('portfolioGrid');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        this.loadMoreBtn = document.getElementById('loadMoreBtn');
        this.init();
    }

    async init() {
        await this.loadProjects();
        this.bindEvents();
        this.renderProjects();
    }

    async loadProjects() {
        try {
            if (window.portfolioDb && window.portfolioDb.db) {
                this.projects = await window.portfolioDb.getAllData('projects');
                // Sort by date (newest first)
                this.projects.sort((a, b) => new Date(b.date) - new Date(a.date));
            } else {
                // Fallback data if database is not available
                this.projects = this.getFallbackProjects();
            }
        } catch (error) {
            console.error('Error loading projects:', error);
            this.projects = this.getFallbackProjects();
        }
    }

    getFallbackProjects() {
        return [
            {
                id: 1,
                title: 'E-Commerce Platform',
                description: 'A full-stack e-commerce solution built with React and Node.js, featuring payment integration, inventory management, and admin dashboard.',
                category: 'web',
                image: 'assets/images/projects/ecommerce.jpg',
                tags: ['React', 'Node.js', 'MongoDB', 'Stripe', 'Express'],
                featured: true,
                date: '2024-01-15',
                githubUrl: 'https://github.com/mohammed-alashrafi/ecommerce-platform',
                liveUrl: 'https://ecommerce-demo.malashrafi.dev'
            },
            {
                id: 2,
                title: 'Task Management App',
                description: 'A collaborative task management application with real-time updates, team collaboration features, and advanced analytics.',
                category: 'web',
                image: 'assets/images/projects/taskapp.jpg',
                tags: ['Vue.js', 'Firebase', 'Vuex', 'Socket.io'],
                featured: true,
                date: '2023-11-20',
                githubUrl: 'https://github.com/mohammed-alashrafi/task-manager',
                liveUrl: 'https://tasks.malashrafi.dev'
            },
            {
                id: 3,
                title: 'Restaurant Mobile App',
                description: 'A React Native mobile application for restaurant ordering with menu browsing, cart management, and order tracking.',
                category: 'mobile',
                image: 'assets/images/projects/restaurant.jpg',
                tags: ['React Native', 'Redux', 'Firebase', 'Maps API'],
                featured: true,
                date: '2023-09-10',
                githubUrl: 'https://github.com/mohammed-alashrafi/restaurant-app',
                liveUrl: 'https://play.google.com/store/apps/details?id=com.restaurant.app'
            }
        ];
    }

    bindEvents() {
        // Filter buttons
        this.filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleFilterClick(e.target);
            });
        });

        // Load more button
        if (this.loadMoreBtn) {
            this.loadMoreBtn.addEventListener('click', () => {
                this.loadMoreProjects();
            });
        }

        // Search functionality
        const searchInput = document.getElementById('projectSearch');
        if (searchInput) {
            searchInput.addEventListener('input', debounce((e) => {
                this.handleSearch(e.target.value);
            }, 300));
        }
    }

    handleFilterClick(button) {
        // Update active filter button
        this.filterButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        // Update current filter
        this.currentFilter = button.dataset.filter;
        this.currentPage = 1;

        // Re-render projects
        this.renderProjects();

        // Track filter usage
        if (window.portfolioDb) {
            window.portfolioDb.trackEvent('portfolio_filter', { filter: this.currentFilter });
        }
    }

    handleSearch(query) {
        this.searchQuery = query.toLowerCase();
        this.currentPage = 1;
        this.renderProjects();

        // Track search
        if (window.portfolioDb && query) {
            window.portfolioDb.trackEvent('portfolio_search', { query });
        }
    }

    getFilteredProjects() {
        let filtered = this.projects;

        // Apply category filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(project => project.category === this.currentFilter);
        }

        // Apply search filter
        if (this.searchQuery) {
            filtered = filtered.filter(project => 
                project.title.toLowerCase().includes(this.searchQuery) ||
                project.description.toLowerCase().includes(this.searchQuery) ||
                project.tags.some(tag => tag.toLowerCase().includes(this.searchQuery))
            );
        }

        return filtered;
    }

    renderProjects() {
        const filteredProjects = this.getFilteredProjects();
        const projectsToShow = filteredProjects.slice(0, this.currentPage * this.projectsPerPage);

        if (!this.portfolioGrid) return;

        // Clear existing projects
        this.portfolioGrid.innerHTML = '';

        // Render projects
        projectsToShow.forEach((project, index) => {
            const projectElement = this.createProjectElement(project, index);
            this.portfolioGrid.appendChild(projectElement);
        });

        // Update load more button
        this.updateLoadMoreButton(filteredProjects.length, projectsToShow.length);

        // Animate new projects
        this.animateProjects();
    }

    createProjectElement(project, index) {
        const projectDiv = document.createElement('div');
        projectDiv.className = 'portfolio-item stagger-item';
        projectDiv.style.animationDelay = `${index * 0.1}s`;

        projectDiv.innerHTML = `
            <div class="portfolio-image">
                <img src="${project.image}" alt="${project.title}" loading="lazy">
                <div class="portfolio-overlay">
                    <div class="portfolio-links">
                        ${project.githubUrl ? `<a href="${project.githubUrl}" class="portfolio-link" target="_blank" rel="noopener" aria-label="View source code">
                            <i class="fab fa-github"></i>
                        </a>` : ''}
                        ${project.liveUrl ? `<a href="${project.liveUrl}" class="portfolio-link" target="_blank" rel="noopener" aria-label="View live demo">
                            <i class="fas fa-external-link-alt"></i>
                        </a>` : ''}
                    </div>
                </div>
            </div>
            <div class="portfolio-content">
                <h3 class="portfolio-title">${project.title}</h3>
                <p class="portfolio-description">${project.description}</p>
                <div class="portfolio-tags">
                    ${project.tags.map(tag => `<span class="portfolio-tag">${tag}</span>`).join('')}
                </div>
                ${project.featured ? '<div class="portfolio-featured"><i class="fas fa-star"></i> Featured</div>' : ''}
            </div>
        `;

        // Add click event for project details
        projectDiv.addEventListener('click', (e) => {
            if (!e.target.closest('.portfolio-link')) {
                this.showProjectDetails(project);
            }
        });

        return projectDiv;
    }

    updateLoadMoreButton(totalFiltered, currentlyShown) {
        if (!this.loadMoreBtn) return;

        if (currentlyShown >= totalFiltered) {
            this.loadMoreBtn.style.display = 'none';
        } else {
            this.loadMoreBtn.style.display = 'inline-flex';
            const remaining = totalFiltered - currentlyShown;
            this.loadMoreBtn.textContent = `Load More Projects (${remaining} remaining)`;
        }
    }

    loadMoreProjects() {
        this.currentPage++;
        this.renderProjects();

        // Track load more
        if (window.portfolioDb) {
            window.portfolioDb.trackEvent('portfolio_load_more', { page: this.currentPage });
        }
    }

    animateProjects() {
        const projectItems = this.portfolioGrid.querySelectorAll('.portfolio-item');
        
        projectItems.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('show');
            }, index * 100);
        });
    }

    showProjectDetails(project) {
        const modal = this.createProjectModal(project);
        document.body.appendChild(modal);
        
        // Animate modal in
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // Track project view
        if (window.portfolioDb) {
            window.portfolioDb.trackEvent('project_view', { projectId: project.id, title: project.title });
        }
    }

    createProjectModal(project) {
        const modal = document.createElement('div');
        modal.className = 'project-modal';
        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <button class="modal-close" aria-label="Close modal">
                    <i class="fas fa-times"></i>
                </button>
                <div class="modal-header">
                    <img src="${project.image}" alt="${project.title}" class="modal-image">
                    <div class="modal-title-section">
                        <h2 class="modal-title">${project.title}</h2>
                        <div class="modal-meta">
                            <span class="modal-date">${this.formatDate(project.date)}</span>
                            <span class="modal-category">${this.formatCategory(project.category)}</span>
                        </div>
                    </div>
                </div>
                <div class="modal-body">
                    <p class="modal-description">${project.description}</p>
                    
                    ${project.technologies ? `
                        <div class="modal-section">
                            <h3>Technologies Used</h3>
                            <div class="modal-tags">
                                ${project.technologies.map(tech => `<span class="modal-tag">${tech}</span>`).join('')}
                            </div>
                        </div>
                    ` : ''}
                    
                    ${project.features ? `
                        <div class="modal-section">
                            <h3>Key Features</h3>
                            <ul class="modal-features">
                                ${project.features.map(feature => `<li>${feature}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    
                    ${project.challenges ? `
                        <div class="modal-section">
                            <h3>Challenges & Solutions</h3>
                            <p>${project.challenges}</p>
                        </div>
                    ` : ''}
                    
                    <div class="modal-actions">
                        ${project.githubUrl ? `
                            <a href="${project.githubUrl}" class="btn btn-outline" target="_blank" rel="noopener">
                                <i class="fab fa-github"></i>
                                View Source
                            </a>
                        ` : ''}
                        ${project.liveUrl ? `
                            <a href="${project.liveUrl}" class="btn btn-primary" target="_blank" rel="noopener">
                                <i class="fas fa-external-link-alt"></i>
                                Live Demo
                            </a>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.modal-close').addEventListener('click', () => {
            this.closeProjectModal(modal);
        });

        modal.querySelector('.modal-backdrop').addEventListener('click', () => {
            this.closeProjectModal(modal);
        });

        // Close on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                this.closeProjectModal(modal);
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        return modal;
    }

    closeProjectModal(modal) {
        modal.classList.add('hide');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long' 
        });
    }

    formatCategory(category) {
        const categories = {
            'web': 'Web Application',
            'mobile': 'Mobile App',
            'design': 'Design Project',
            'api': 'API Development',
            'tool': 'Development Tool'
        };
        return categories[category] || category;
    }

    // Method to add new project (for future admin functionality)
    async addProject(projectData) {
        try {
            if (window.portfolioDb) {
                const id = await window.portfolioDb.addData('projects', projectData);
                projectData.id = id;
                this.projects.unshift(projectData);
                this.renderProjects();
                return id;
            }
        } catch (error) {
            console.error('Error adding project:', error);
            throw error;
        }
    }

    // Method to update project
    async updateProject(projectId, updates) {
        try {
            if (window.portfolioDb) {
                const project = this.projects.find(p => p.id === projectId);
                if (project) {
                    Object.assign(project, updates);
                    await window.portfolioDb.updateData('projects', project);
                    this.renderProjects();
                }
            }
        } catch (error) {
            console.error('Error updating project:', error);
            throw error;
        }
    }

    // Method to delete project
    async deleteProject(projectId) {
        try {
            if (window.portfolioDb) {
                await window.portfolioDb.deleteData('projects', projectId);
                this.projects = this.projects.filter(p => p.id !== projectId);
                this.renderProjects();
            }
        } catch (error) {
            console.error('Error deleting project:', error);
            throw error;
        }
    }

    // Get portfolio statistics
    getStatistics() {
        return {
            totalProjects: this.projects.length,
            webProjects: this.projects.filter(p => p.category === 'web').length,
            mobileProjects: this.projects.filter(p => p.category === 'mobile').length,
            designProjects: this.projects.filter(p => p.category === 'design').length,
            featuredProjects: this.projects.filter(p => p.featured).length,
            latestProject: this.projects[0],
            technologies: [...new Set(this.projects.flatMap(p => p.tags))].sort()
        };
    }
}

// Blog/Articles Manager
class BlogManager {
    constructor() {
        this.posts = [];
        this.blogContainer = document.getElementById('blogContainer');
        this.init();
    }

    async init() {
        await this.loadPosts();
        this.renderPosts();
    }

    async loadPosts() {
        try {
            if (window.portfolioDb && window.portfolioDb.db) {
                this.posts = await window.portfolioDb.getByIndex('blogPosts', 'published', 1);
                this.posts.sort((a, b) => new Date(b.date) - new Date(a.date));
            }
        } catch (error) {
            console.error('Error loading blog posts:', error);
        }
    }

    renderPosts() {
        if (!this.blogContainer || this.posts.length === 0) return;

        this.blogContainer.innerHTML = this.posts.map(post => `
            <article class="blog-post">
                <div class="post-meta">
                    <span class="post-date">${this.formatDate(post.date)}</span>
                    <span class="post-read-time">${post.readTime} min read</span>
                </div>
                <h3 class="post-title">${post.title}</h3>
                <p class="post-excerpt">${post.excerpt}</p>
                <div class="post-tags">
                    ${post.tags.map(tag => `<span class="post-tag">${tag}</span>`).join('')}
                </div>
                <a href="#" class="post-read-more" data-post-id="${post.id}">Read More</a>
            </article>
        `).join('');

        // Add click events for read more
        this.blogContainer.querySelectorAll('.post-read-more').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const postId = parseInt(e.target.dataset.postId);
                this.showPost(postId);
            });
        });
    }

    showPost(postId) {
        const post = this.posts.find(p => p.id === postId);
        if (post) {
            // Create and show post modal
            const modal = this.createPostModal(post);
            document.body.appendChild(modal);
            
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            // Track post view
            if (window.portfolioDb) {
                window.portfolioDb.trackEvent('blog_post_view', { postId, title: post.title });
            }
        }
    }

    createPostModal(post) {
        const modal = document.createElement('div');
        modal.className = 'post-modal';
        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <button class="modal-close" aria-label="Close modal">
                    <i class="fas fa-times"></i>
                </button>
                <article class="post-content">
                    <header class="post-header">
                        <h1 class="post-title">${post.title}</h1>
                        <div class="post-meta">
                            <span class="post-author">By ${post.author}</span>
                            <span class="post-date">${this.formatDate(post.date)}</span>
                            <span class="post-read-time">${post.readTime} min read</span>
                        </div>
                        <div class="post-tags">
                            ${post.tags.map(tag => `<span class="post-tag">${tag}</span>`).join('')}
                        </div>
                    </header>
                    <div class="post-body">
                        ${this.formatPostContent(post.content)}
                    </div>
                </article>
            </div>
        `;

        // Add close functionality
        modal.querySelector('.modal-close').addEventListener('click', () => {
            this.closePostModal(modal);
        });

        modal.querySelector('.modal-backdrop').addEventListener('click', () => {
            this.closePostModal(modal);
        });

        return modal;
    }

    closePostModal(modal) {
        modal.classList.add('hide');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
    }

    formatPostContent(content) {
        // Simple markdown-like formatting
        return content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/```(.*?)```/gs, '<code>$1</code>');
    }
}

// Initialize managers when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for database to be ready
    const initManagers = () => {
        new PortfolioManager();
        new BlogManager();
    };

    if (window.portfolioDb && window.portfolioDb.db) {
        initManagers();
    } else {
        // Wait for database to initialize
        setTimeout(initManagers, 1000);
    }
});

// Export for global use
window.portfolioManager = {
    PortfolioManager,
    BlogManager
};