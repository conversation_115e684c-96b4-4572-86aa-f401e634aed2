<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: red;
            background: #ffeaea;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Portfolio Website Test</h1>
        <p>Testing the portfolio files...</p>
        
        <div id="results"></div>
        
        <button onclick="testFiles()">Test Files</button>
        <button onclick="openMain()">Open Main Site</button>
    </div>

    <script>
        function testFiles() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Testing files...</p>';
            
            const tests = [
                { name: 'Main CSS', path: 'styles/main.css' },
                { name: 'Components CSS', path: 'styles/components.css' },
                { name: 'Animations CSS', path: 'styles/animations.css' },
                { name: 'Responsive CSS', path: 'styles/responsive.css' },
                { name: 'Database JS', path: 'js/database.js' },
                { name: 'Utils JS', path: 'js/utils.js' },
                { name: 'Animations JS', path: 'js/animations.js' },
                { name: 'Portfolio JS', path: 'js/portfolio.js' },
                { name: 'Main JS', path: 'js/main.js' },
                { name: 'Service Worker', path: 'sw.js' }
            ];
            
            let html = '<h3>File Test Results:</h3>';
            
            tests.forEach(test => {
                fetch(test.path)
                    .then(response => {
                        if (response.ok) {
                            html += `<div class="success">✓ ${test.name} - OK</div>`;
                        } else {
                            html += `<div class="error">✗ ${test.name} - Failed (${response.status})</div>`;
                        }
                        results.innerHTML = html;
                    })
                    .catch(error => {
                        html += `<div class="error">✗ ${test.name} - Error: ${error.message}</div>`;
                        results.innerHTML = html;
                    });
            });
        }
        
        function openMain() {
            window.open('index.html', '_blank');
        }
    </script>
</body>
</html>